package com.bestseller.storeorderfulfillmentservice.configuration.brand;

import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
class BrandPropertiesConfigurationTest {

    @Autowired
    private BrandPropertiesConfiguration brandPropertiesConfiguration;

    @ParameterizedTest
    @MethodSource("datasource")
    void getBrandProperty_givenBrand_returnsBrandProperty(String brand, String expectedBrandName) {
        // act & assert
        assertThat(brandPropertiesConfiguration.getBrandProperty(brand).getName())
            .isEqualTo(expectedBrandName);
    }

    public static Stream<Arguments> datasource() {
        return Stream.of(
            Arguments.of("JJ", "JACK & JONES"),
            Arguments.of("FOO", "BESTSELLER"),
            Arguments.of(null, "BESTSELLER")
        );
    }
}
