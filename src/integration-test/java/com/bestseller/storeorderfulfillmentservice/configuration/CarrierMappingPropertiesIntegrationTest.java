package com.bestseller.storeorderfulfillmentservice.configuration;

import com.bestseller.storeorderfulfillmentservice.exception.CarrierNotSupported;
import com.bestseller.storeorderfulfillmentservice.model.DeliveryType;
import com.bestseller.storeorderfulfillmentservice.model.ServiceType;
import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.stream.Stream;

import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.HOME;
import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.LOCKER;
import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.PICKUP;
import static com.bestseller.storeorderfulfillmentservice.model.ServiceType.EXPRESS;
import static com.bestseller.storeorderfulfillmentservice.model.ServiceType.STANDARD;
import static com.neovisionaries.i18n.CountryCode.BE;
import static com.neovisionaries.i18n.CountryCode.DE;
import static com.neovisionaries.i18n.CountryCode.DK;
import static com.neovisionaries.i18n.CountryCode.NL;
import static com.neovisionaries.i18n.CountryCode.NO;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
class CarrierMappingPropertiesIntegrationTest {

    private static final String DHL = "DHL";
    private static final String POSTNL = "POSTNL";
    private static final String DHL_BENELUX = "DHL_BENELUX";
    private static final String DHL_V_2 = "DHL_V2";
    private static final String POST_NORD = "POST_NORD";

    @Autowired
    private CarrierMappingProperties carrierMappingProperties;

    @ParameterizedTest
    @MethodSource("countryMappingData")
    void getCarrier_allPossibleMappingsAreReturned(String country,
                                                   DeliveryType deliveryType,
                                                   ServiceType serviceType,
                                                   String ecommerceCarrier,
                                                   Carrier expectedCarrier) {
        // act & assert
        assertThat(carrierMappingProperties
            .getCarrier(getDeliveryOptionQuery(country, deliveryType, serviceType, ecommerceCarrier)))
            .isEqualTo(expectedCarrier);
    }

    @Test
    void getCarrier_givenInvalidQuery_errorIsReturned() {
        String country = NO.getAlpha2();

        // act & assert
        assertThatThrownBy(() -> carrierMappingProperties.getCarrier(getDeliveryOptionQuery(country, PICKUP, STANDARD, "FOO")))
            .isInstanceOf(CarrierNotSupported.class)
            .hasMessage("Carrier not found for given country: NO, carrier: FOO, deliveryOption: PICKUP and serviceType: STANDARD");
    }

    private DeliveryOptionQuery getDeliveryOptionQuery(String country, DeliveryType deliveryType, ServiceType serviceType, String carrier) {
        return DeliveryOptionQuery.builder()
            .country(country)
            .deliveryType(deliveryType)
            .serviceType(serviceType)
            .carrier(carrier)
            .build();
    }

    private static Stream<Arguments> countryMappingData() {
        return Stream.of(
            // Netherlands
            Arguments.of(NL.getAlpha2(), HOME, STANDARD, DHL, new Carrier(DHL_BENELUX, null)),
            Arguments.of(NL.getAlpha2(), HOME, STANDARD, POSTNL, new Carrier(POSTNL, null)),
            Arguments.of(NL.getAlpha2(), HOME, EXPRESS, DHL, new Carrier(DHL_BENELUX, null)),
            Arguments.of(NL.getAlpha2(), HOME, EXPRESS, POSTNL, new Carrier(POSTNL, null)),
            Arguments.of(NL.getAlpha2(), PICKUP, STANDARD, DHL, new Carrier(DHL_BENELUX, null)),
            Arguments.of(NL.getAlpha2(), PICKUP, STANDARD, POSTNL, new Carrier(POSTNL, null)),

            // Germany
            Arguments.of(DE.getAlpha2(), HOME, STANDARD, DHL, new Carrier(DHL_V_2, null)),
            Arguments.of(DE.getAlpha2(), HOME, EXPRESS, DHL, new Carrier(DHL_V_2, null)),
            Arguments.of(DE.getAlpha2(), PICKUP, STANDARD, DHL, new Carrier(DHL_V_2, null)),
            Arguments.of(DE.getAlpha2(), LOCKER, STANDARD, DHL, new Carrier(DHL_V_2, null)),

            // Norway
            Arguments.of(NO.getAlpha2(), PICKUP, STANDARD, "BRINGZOLL", new Carrier("BRING", null)),

            // Denmark
            Arguments.of(DK.getAlpha2(), PICKUP, STANDARD, "POSTNORD", new Carrier(POST_NORD, null)),
            Arguments.of(DK.getAlpha2(), PICKUP, STANDARD, "GLS", new Carrier("GLS", null)),
            Arguments.of(DK.getAlpha2(), PICKUP, STANDARD, "POSTDK", new Carrier(POST_NORD, null)),
            Arguments.of(DK.getAlpha2(), HOME, STANDARD, "POSTNORD", new Carrier(POST_NORD, null)),
            Arguments.of(DK.getAlpha2(), HOME, STANDARD, "POSTDK", new Carrier(POST_NORD, null)),
            Arguments.of(DK.getAlpha2(), HOME, EXPRESS, "POSTNORD", new Carrier(POST_NORD, null)),
            Arguments.of(DK.getAlpha2(), HOME, EXPRESS, "POSTDK", new Carrier(POST_NORD, null)),

            // Belgium
            Arguments.of(BE.getAlpha2(), PICKUP, STANDARD, "BPOSTAPI", new Carrier("BPOST", null)),
            Arguments.of(BE.getAlpha2(), HOME, STANDARD, "BPOSTAPI", new Carrier("BPOST", null)),
            Arguments.of(BE.getAlpha2(), HOME, EXPRESS, "DHL", new Carrier(DHL_BENELUX, null)),
            Arguments.of(BE.getAlpha2(), LOCKER, STANDARD, "BPOSTAPI", new Carrier("BPOST", null))
        );
    }
}
