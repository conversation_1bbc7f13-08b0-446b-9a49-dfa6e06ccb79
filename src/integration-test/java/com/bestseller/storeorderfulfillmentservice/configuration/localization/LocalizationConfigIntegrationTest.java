package com.bestseller.storeorderfulfillmentservice.configuration.localization;

import com.bestseller.storeorderfulfillmentservice.exception.LocalizationNotFound;
import com.bestseller.storeorderfulfillmentservice.model.localization.Localization;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
class LocalizationConfigIntegrationTest {

    @Autowired
    private LocalizationConfig localizationConfig;

    @Test
    void getInvoiceLocalizationByCountry_givenValidCountry_localizationReturned() {
        // arrange
        var country = "NO";

        // act
        Localization localization = localizationConfig.getLocalizationByCountry(country);

        // assert
        assertThat(localization).as("Invoice localization was found")
                .isNotNull();
    }

    @Test
    void getInvoiceLocalizationByCountry_givenNotLocalizedValidCountry_noLocalizationIsReturned() {
        // arrange
        var country = "BR";

        // act & assert
        var localizationNotFound = assertThrows(
                LocalizationNotFound.class,
                () -> localizationConfig.getLocalizationByCountry(country));
        assertThat(localizationNotFound).as("Invoice localization was found")
                .hasMessageContaining("Localization for the country BR not found");
    }
}
