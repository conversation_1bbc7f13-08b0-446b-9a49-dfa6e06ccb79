package com.bestseller.storeorderfulfillmentservice.configuration;

import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
class TemplateCarrierMappingPropertiesTest {

    @Autowired
    private TemplateCarrierMappingProperties templateCarrierMappingProperties;

    @ParameterizedTest
    @NullAndEmptySource
    void getTemplateCarrierValue_givenNullValue_returnsNull(String carrierKey) {
        // act
        var templateCarrierValue = templateCarrierMappingProperties.getTemplateCarrierValue(carrierKey);

        // assert
        assertThat(templateCarrierValue).isNull();
    }

    @ParameterizedTest
    @MethodSource("carrierMappingData")
    void getTemplateCarrierValue_givenValidCarrierKey_returnsTemplateCarrierValue(
        String carrierKey,
        String expectedTemplateCarrierValue
    ) {
        // act
        var templateCarrierValue = templateCarrierMappingProperties.getTemplateCarrierValue(carrierKey);

        // assert
        assertThat(templateCarrierValue).isEqualTo(expectedTemplateCarrierValue);
    }

    private static Stream<Arguments> carrierMappingData() {
        return Stream.of(
            Arguments.of("DHL_BENELUX", "DHL"),
            Arguments.of("POSTNL", "PostNL"),
            Arguments.of("POST_NORD", "PostNord"),
            Arguments.of("GLS", "GLS")
        );
    }
}
