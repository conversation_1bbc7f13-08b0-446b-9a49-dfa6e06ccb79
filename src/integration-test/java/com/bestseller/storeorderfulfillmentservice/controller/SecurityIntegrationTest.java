package com.bestseller.storeorderfulfillmentservice.controller;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.reactive.server.WebTestClient;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("dev")
class SecurityIntegrationTest {

    @Autowired
    private WebTestClient webTestClient;

    @Test
    void healthCheck_anonymousUser_isOk() {
        // arrange - nothing

        // act
        webTestClient.get()
                .uri("/actuator/health")
                .exchange()

                // assert
                .expectStatus()
                .isOk();
    }

    @Test
    void actuator_anonymousUser_isUnauthorized() {
        // arrange - nothing

        // act
        webTestClient.get()
                .uri("/actuator")
                .exchange()

                // assert
                .expectStatus()
                .isUnauthorized();
    }

    @Test
    void actuator_admin_isOk() {
        // arrange - nothing

        // act
        webTestClient.get()
                .uri("/actuator")
                .headers(headers -> headers.setBasicAuth("admin", "admin"))
                .exchange()
                // assert
                .expectStatus()
                .isOk();
    }

}
