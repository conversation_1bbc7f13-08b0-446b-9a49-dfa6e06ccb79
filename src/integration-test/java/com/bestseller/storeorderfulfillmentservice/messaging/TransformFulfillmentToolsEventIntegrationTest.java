package com.bestseller.storeorderfulfillmentservice.messaging;

import com.amazonaws.services.s3.AmazonS3;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.fulfillmenttoolseventsreceived.FulfillmentToolsEventsReceived;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.Article;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.ConsumerInformation;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.FacilityAddress;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.LineItem;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.OrderCancelled;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.dispatched.HandoverJobHandedOver;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.notroutable.RoutingPlanNotRoutable;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.ConsumerAddress;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.PackJobCreated;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobAborted;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobCreated;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobPickingFinished;
import com.bestseller.storeorderfulfillmentservice.util.Topics;
import com.bestseller.testutils.util.TestConsumer;
import com.github.tomakehurst.wiremock.client.WireMock;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;
import org.apache.kafka.common.serialization.VoidSerializer;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerializer;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import util.PackJobCreatedPayloadGenerator;

import java.time.Instant;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.hamcrest.Matchers.hasSize;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {
    // auto-created topics have messages before consumers subscribe to them
    "spring.cloud.stream.kafka.default.consumer.start-offset=earliest",

    // this test produces to kafka and should therefore be isolated from other tests,
    // cached Spring contexts will eat its messages otherwise
    "kafka.topic.prefix=TransformFulfillmentToolsEventIntegrationTest-"
})
@ActiveProfiles("dev")
@Slf4j
class TransformFulfillmentToolsEventIntegrationTest {
    private static final String FACILITY_REFERENCE = "2A5AA323-D065-4A94-9F04-77454CFD6A45";
    private static final int LOCATION_CHAIN_CODE = 711;

    @Value("${spring.cloud.stream.kafka.binder.brokers}:9092")
    private String kafkaBrokerList;
    @Autowired
    private Topics topics;

    @Value("${wiremock.port}")
    private int wiremockPort;
    @Value("${wiremock.host}")
    private String wiremockHost;

    @Value("${s3.bucket}")
    private String bucket;
    @Autowired
    private AmazonS3 s3;

    private KafkaProducer<Void, Object> producer;
    private TestConsumer<OrderLineAcknowledged> orderLineAcknowledgedTestConsumer;
    private TestConsumer<OrderLineDispatched> orderLineDispatchedTestConsumer;
    private TestConsumer<OrderPartRejected> orderPartRejectedTestConsumer;
    private TestConsumer<OrderPartsCancelled> orderPartsCancelledTestConsumer;

    @BeforeEach
    void arrangeEmptyBucket() {
        if (!s3.doesBucketExistV2(bucket)) {
            s3.createBucket(bucket, "east-1");
        }
        s3.listObjects(bucket).getObjectSummaries().forEach(object ->
            s3.deleteObject(bucket, object.getKey()));
        assertThat(s3.listObjects(bucket).getObjectSummaries())
            .as("objects left after deletion")
            .isEmpty();
    }

    @BeforeEach
    void initializeKafkaClient() {
        producer = new KafkaProducer<>(Map.of(
            ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, VoidSerializer.class,
            ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class,
            ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBrokerList
        ));

        orderLineAcknowledgedTestConsumer = new TestConsumer<>(topics.getOrderLineAcknowledged(),
            new JsonDeserializer<>(OrderLineAcknowledged.class));
        orderLineDispatchedTestConsumer = new TestConsumer<>(topics.getOrderLineDispatched(),
            new JsonDeserializer<>(OrderLineDispatched.class));
        orderPartRejectedTestConsumer = new TestConsumer<>(topics.getOrderPartRejected(),
            new JsonDeserializer<>(OrderPartRejected.class));
        orderPartsCancelledTestConsumer = new TestConsumer<>(topics.getOrderPartsCancelled(),
            new JsonDeserializer<>(OrderPartsCancelled.class));
    }

    @BeforeEach
    void configureWiremock() {
        WireMock.configureFor(wiremockHost, wiremockPort);
        WireMock.resetAllRequests();
    }

    @AfterEach
    void closeProducer() {
        producer.close();
    }

    @Test
    void transformFulfillmentToolsEvent_givenPickJobCreated_orderLineAcknowledgedProduced()
        throws ExecutionException, InterruptedException {
        //arrange
        String ean = "foo";
        var pickJobCreated = new FulfillmentToolsEventsReceived()
            .withEvent(PickJobCreated.NAME)
            .withEventId("703F3C89-67BA-464F-B1BE-B1FD506BCC88")
            .withPayload(PickJobCreated.builder()
                .facilityRef(FACILITY_REFERENCE)
                .created(Instant.EPOCH)
                .pickLineItems(List.of(LineItem.builder()
                    .article(new Article(ean))
                    .quantity(1)
                    .build()))
                .build());
        Iterator<OrderLineAcknowledged> orderLineAcknowledged = orderLineAcknowledgedTestConsumer.consumeFromHere();

        // act
        producer.send(new ProducerRecord<>(topics.getFulfillmentToolsEventsReceived(), null, pickJobCreated)).get();

        //assert
        assertThat(orderLineAcknowledged.next())
            .as("OrderLineAcknowledged")
            .satisfies(msg -> {
                assertThat(msg.getEan()).as("ean").isEqualTo(ean);
                assertThat(msg.getStoreId()).as("store").isEqualTo(LOCATION_CHAIN_CODE);
            });
    }

    @Test
    void transformFulfillmentToolsEvent_givenHandover_orderLineDispatchedProduced()
        throws ExecutionException, InterruptedException {
        // arrange
        String ean = "5715371213830";
        String parcel = "LOST";
        String returnLabel = "4-8-15-16-23-42";
        String pickJob = "PPIICCKK-JJJJ-OOOO-BBBB-RRRREEEEFFFF";

        var handedOver = new FulfillmentToolsEventsReceived()
            .withEvent(HandoverJobHandedOver.NAME)
            .withEventId("202F6666-67BA-464F-B1BE-B1FD506BBB77")
            .withPayload(HandoverJobHandedOver.builder()
                .facilityRef(FACILITY_REFERENCE)
                .parcelRef(parcel)
                .created(Instant.EPOCH)
                .pickJobRef(pickJob)
                .carrierRef("CARRIER_REF")
                .build());

        Iterator<OrderLineDispatched> orderLineDispatched = orderLineDispatchedTestConsumer.consumeFromHere();

        // act
        producer.send(new ProducerRecord<>(topics.getFulfillmentToolsEventsReceived(), null, handedOver)).get();

        // assert
        assertThat(orderLineDispatched.next())
            .as("OrderLineDispatched")
            .satisfies(msg -> {
                assertThat(msg.getEan()).as("EAN").isEqualTo(ean);
                assertThat(msg.getReturnShipmentId()).as("label").isEqualTo(returnLabel);
            });
    }

    @Test
    void transformFulfillmentToolsEvent_givenCannotRoute_orderPartRejectedProduced() throws ExecutionException, InterruptedException {
        // arrange
        String ean = "baz";
        String country = "HM";

        var notRoutable = new FulfillmentToolsEventsReceived()
            .withEvent(RoutingPlanNotRoutable.NAME)
            .withEventId("456F3C66-55GH-464F-B1BE-B1FD506BAA23")
            .withPayload(RoutingPlanNotRoutable.builder()
                .targetAddress(FacilityAddress.builder().country(country).build())
                .created(Instant.EPOCH)
                .orderLineItems(List.of(LineItem.builder()
                    .article(new Article(ean))
                    .quantity(1)
                    .build()))
                .build());

        Iterator<OrderPartRejected> orderPartRejected = orderPartRejectedTestConsumer.consumeFromHere();

        // act
        producer.send(new ProducerRecord<>(topics.getFulfillmentToolsEventsReceived(), null, notRoutable)).get();

        // assert
        assertThat(orderPartRejected.next())
            .as("OrderPartRejected")
            .satisfies(msg -> {
                assertThat(msg.getOrderLines())
                    .as("order lines")
                    .map(OrderLine::getEan)
                    .containsExactly(ean);
                assertThat(msg.getWarehouse())
                    .as("warehouse")
                    .endsWith(country);
            });
    }

    @Test
    void transformFulfillmentToolsEvent_givenPickJobAborted_orderPartRejectedProduced() throws ExecutionException, InterruptedException {
        //arrange
        final String ean = "test";
        final String ecomOrderId = "1234";

        var pickJobAborted = new FulfillmentToolsEventsReceived()
            .withEvent(PickJobAborted.NAME)
            .withEventId("703F3C89-67BA-464F-B1BE-B1FD506BCC87")
            .withPayload(PickJobAborted.builder()
                .pickLineItems(List.of(LineItem
                    .builder()
                    .quantity(1)
                    .article(new Article(ean))
                    .build())
                )
                .orderDate(Instant.MIN)
                .created(Instant.EPOCH)
                .tenantOrderId(ecomOrderId)
                .orderId("IDIDIDID-NNNN-OOOO-TTTT-EEECCCOOOMMM")
                .facilityRef(FACILITY_REFERENCE)
                .build());

        Iterator<OrderPartRejected> orderPartRejected = orderPartRejectedTestConsumer.consumeFromHere();

        // act
        producer.send(new ProducerRecord<>(topics.getFulfillmentToolsEventsReceived(), null, pickJobAborted)).get();

        //assert
        assertThat(orderPartRejected.next())
            .as("OrderPartRejected from aborted")
            .satisfies(msg -> {
                assertThat(msg.getOrderLines().get(0).getEan()).as("EAN").isEqualTo(ean);
                assertThat(msg.getOrderId()).as("Order Id").isEqualTo(ecomOrderId);
            });
    }

    @Test
    void transformFulfillmentToolsEvent_givenOrderCancelled_orderPartRejectedProduced() throws ExecutionException, InterruptedException {
        //arrange
        final String ean = "test1";
        final String ecomOrderId = "12345";
        OrderCancelled orderCancelled = OrderCancelled.builder()
            .orderLineItems(List.of(LineItem
                .builder()
                .quantity(1)
                .article(new Article(ean))
                .build())
            )
            .orderDate(Instant.MIN)
            .created(Instant.EPOCH)
            .tenantOrderId(ecomOrderId)
            .consumer(ConsumerInformation.builder()
                .addresses(List.of(ConsumerAddress.builder()
                    .country("NL")
                    .build()))
                .build())
            .build();

        var orderCancelledEvent = new FulfillmentToolsEventsReceived()
            .withEvent(OrderCancelled.NAME)
            .withEventId("101F3C89-67BA-464F-B1BE-C1FD506BCC00")
            .withPayload(orderCancelled);

        Iterator<OrderPartRejected> orderPartRejected = orderPartRejectedTestConsumer.consumeFromHere();

        // act
        producer.send(new ProducerRecord<>(topics.getFulfillmentToolsEventsReceived(), null, orderCancelledEvent)).get();

        //assert
        assertThat(orderPartRejected)
            .as("OrderPartRejected from OrderCancelled")
            .hasNext()
            .satisfies(
                msg -> {
                    var nextEvent = msg.next();
                    assertThat(nextEvent.getOrderLines().get(0).getEan()).as("EAN").isEqualTo(ean);
                    assertThat(nextEvent.getWarehouse()).as("Warehouse").isEqualTo("SHIP_FROM_STORE_NL");
                    assertThat(nextEvent.getOrderId()).as("Order Id").isEqualTo(ecomOrderId);
                }
            );
    }

    @Test
    void transformFulfillmentToolsEvent_malformedJson_nextMessagesProcessed() throws ExecutionException, InterruptedException {
        // arrange
        String ean = "qux";
        var notRoutable = new FulfillmentToolsEventsReceived()
            .withEvent(RoutingPlanNotRoutable.NAME)
            .withEventId("foobar-baz-qux-quux-corgegrault")
            .withPayload(RoutingPlanNotRoutable.builder()
                .targetAddress(FacilityAddress.builder().country("XY").build())
                .created(Instant.EPOCH)
                .orderLineItems(List.of(LineItem.builder()
                    .article(new Article(ean))
                    .quantity(1)
                    .build()))
                .build());

        Iterator<OrderPartRejected> orderPartRejected = orderPartRejectedTestConsumer.consumeFromHere();
        @Cleanup var rawProducer = new KafkaProducer<Void, Object>(Map.of(
            ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, VoidSerializer.class,
            ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class,
            ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBrokerList
        ));

        // act
        rawProducer.send(new ProducerRecord<>(topics.getFulfillmentToolsEventsReceived(), null, "<not a=json/>")).get();
        producer.send(new ProducerRecord<>(topics.getFulfillmentToolsEventsReceived(), null, notRoutable)).get();

        // assert
        assertThat(orderPartRejected.next())
            .as("OrderPartRejected")
            .isNotNull();
    }

    @Test
    void transformFulfillmentToolsEvent_givenPackJobCreated_pdfFileIsSavedInS3() throws ExecutionException, InterruptedException {
        // arrange
        PackJobCreated payload = PackJobCreatedPayloadGenerator.generate();
        payload.setFacilityRef(FACILITY_REFERENCE);
        payload.setPickJobRef("79E25AE1-D525-490F-86CA-A99AA68733BD");
        var event = new FulfillmentToolsEventsReceived()
            .withEvent(PackJobCreated.NAME)
            .withEventId("C92D9257-D521-4E0A-A34E-BE1E360AAC24")
            .withPayload(payload);
        // act
        producer.send(new ProducerRecord<>(topics.getFulfillmentToolsEventsReceived(), null, event)).get();

        // assert
        await("Until file is present in s3")
            .until(() -> s3.listObjects(bucket).getObjectSummaries(), hasSize(1));
    }

    @Test
    void transformFulfillmentToolsEvent_givenPickJobPickingFinished_orderPartsCancelledProduced() throws ExecutionException, InterruptedException {
        // arrange
        String ean = "eantest";

        var pickJobPickingFinished = new FulfillmentToolsEventsReceived()
            .withEvent(PickJobPickingFinished.NAME)
            .withEventId("703F3RE2-67BA-464F-B1BE-B1FD506BJU11")
            .withPayload(PickJobPickingFinished.builder()
                .created(Instant.EPOCH)
                .facilityRef(FACILITY_REFERENCE)
                .pickLineItems(List.of(LineItem.builder()
                    .article(new Article(ean))
                    .quantity(1)
                    .picked(0)
                    .build()))
                .build());

        Iterator<OrderPartsCancelled> orderPartsCancelled = orderPartsCancelledTestConsumer.consumeFromHere();

        // act
        producer.send(new ProducerRecord<>(topics.getFulfillmentToolsEventsReceived(), null, pickJobPickingFinished)).get();

        // assert
        assertThat(orderPartsCancelled.next())
            .as("OrderPartsCancelled")
            .satisfies(msg -> {
                assertThat(msg.getOrderLines())
                    .as("order lines")
                    .map(com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderLine::getEan)
                    .containsExactly(ean);
                assertThat(msg.getWarehouse())
                    .as("warehouse")
                    .endsWith("NL");
            });
    }

    @Test
    void transformFulfillmentToolsEvent_givenPickJobPickingFinished_noOrderPartsCancelledProduced() throws ExecutionException, InterruptedException {
        // arrange
        var pickJobPickingFinished = new FulfillmentToolsEventsReceived()
            .withEvent(PickJobPickingFinished.NAME)
            .withEventId("703F3RE2-67BA-464F-B1BE-B1FD506BJU11")
            .withPayload(PickJobPickingFinished.builder()
                .created(Instant.EPOCH)
                .facilityRef(FACILITY_REFERENCE)
                .pickLineItems(List.of(LineItem.builder()
                    .article(new Article("eantest"))
                    .quantity(1)
                    .picked(1)
                    .build()))
                .build());

        Iterator<OrderPartsCancelled> orderPartsCancelled = orderPartsCancelledTestConsumer.consumeFromHere();

        // act
        producer.send(new ProducerRecord<>(topics.getFulfillmentToolsEventsReceived(), null, pickJobPickingFinished)).get();

        // assert
        assertThat(orderPartsCancelled).as("OrderPartsCancelled is not produced").isExhausted();
    }
}
