package com.bestseller.storeorderfulfillmentservice.messaging;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.storeorderfulfillmentservice.util.Topics;
import com.bestseller.testutils.util.TestConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.VoidSerializer;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerializer;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import util.OrderPartsCreatedGenerator;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ExecutionException;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {
    // auto-created topics have messages before consumers subscribe to them
    "spring.cloud.stream.kafka.default.consumer.start-offset=earliest",

    // this test produces to kafka and should therefore be isolated from other tests,
    // cached Spring contexts will eat its messages otherwise
    "kafka.topic.prefix=ExportOrderToFulfillmentToolsIntegrationTest-"
})
@ActiveProfiles("dev")
@Slf4j
class ExportOrderToFulfillmentToolsIntegrationTest {

    @Value("${spring.cloud.stream.kafka.binder.brokers}:9092")
    private String kafkaBrokerList;

    @Autowired
    private Topics topics;

    private KafkaProducer<Void, Object> producer;

    private TestConsumer<OrderLineExported> orderLineExportedTestConsumer;

    private TestConsumer<OrderPartRejected> orderPartRejectedTestConsumer;

    @BeforeEach
    void initializeKafkaClient() {
        producer = new KafkaProducer<>(Map.of(
            ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, VoidSerializer.class,
            ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class,
            ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBrokerList
        ));
        orderLineExportedTestConsumer = new TestConsumer<>(
            topics.getOrderLineExported(),
            new JsonDeserializer<>(OrderLineExported.class));

        orderPartRejectedTestConsumer = new TestConsumer<>(
            topics.getOrderPartRejected(),
            new JsonDeserializer<>(OrderPartRejected.class));
    }

    @AfterEach
    void closeProducer() {
        producer.close();
    }

    @Test
    void exportOrderToFulfillmentTools_givenOrderPartsCreated_orderLineExportedIsGenerated()
        throws ExecutionException, InterruptedException {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        var invalidCarrierOrderPartsCreated = OrderPartsCreatedGenerator.generate();
        invalidCarrierOrderPartsCreated.getOrderDetails().setCarrier("INVALID CARRIER");

        Iterator<OrderLineExported> orderLineExportedIterator = orderLineExportedTestConsumer.consumeFromHere();
        Iterator<OrderPartRejected> rejectedIterator = orderPartRejectedTestConsumer.consumeFromHere();

        // act
        producer.send(new ProducerRecord<>(topics.getOrderPartsCreated(), invalidCarrierOrderPartsCreated)).get();
        producer.send(new ProducerRecord<>(topics.getOrderPartsCreated(), invalidCarrierOrderPartsCreated)).get();
        producer.send(new ProducerRecord<>(topics.getOrderPartsCreated(), orderPartsCreated)).get();
        producer.send(new ProducerRecord<>(topics.getOrderPartsCreated(), invalidCarrierOrderPartsCreated)).get();


        // assert
        assertThat(orderLineExportedIterator.next())
            .as("OrderLineExported 1")
            .satisfies(msg -> {
                assertThat(msg.getEan()).as("First event EAN").isEqualTo(OrderPartsCreatedGenerator.EAN_1);
                assertThat(msg.getQuantity()).as("First event quantity")
                    .isEqualTo(OrderPartsCreatedGenerator.QUANTITY_1);
            });

        assertThat(orderLineExportedIterator.next())
            .as("OrderLineExported 2")
            .satisfies(msg -> {
                assertThat(msg.getEan()).as("Second event EAN").isEqualTo(OrderPartsCreatedGenerator.EAN_2);
                assertThat(msg.getQuantity()).as("Second event quantity")
                    .isEqualTo(OrderPartsCreatedGenerator.QUANTITY_2);
            });

        assertThat(rejectedIterator.next())
            .as("OrderPartsRejected 1")
            .isNotNull();
        assertThat(rejectedIterator.next())
            .as("OrderLineExported 2")
            .isNotNull();
        assertThat(rejectedIterator.next())
            .as("OrderLineExported 3")
            .isNotNull();
    }

}
