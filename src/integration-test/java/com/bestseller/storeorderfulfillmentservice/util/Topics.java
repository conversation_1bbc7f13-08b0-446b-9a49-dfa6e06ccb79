package com.bestseller.storeorderfulfillmentservice.util;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Utility class containing all the necessary topics.
 */
@Component
@Getter
public class Topics {

    @Value("${spring.cloud.stream.bindings.transformFulfillmentToolsEvent-in-0.destination}")
    private String fulfillmentToolsEventsReceived;

    @Value("${spring.cloud.stream.bindings.transformFulfillmentToolsEvent-out-0.destination}")
    private String orderLineAcknowledged;

    @Value("${spring.cloud.stream.bindings.transformFulfillmentToolsEvent-out-1.destination}")
    private String orderLineDispatched;

    @Value("${spring.cloud.stream.bindings.transformFulfillmentToolsEvent-out-2.destination}")
    private String orderPartRejected;

    @Value("${spring.cloud.stream.bindings.exportOrderToFulfillmentTools-in-0.destination}")
    private String orderPartsCreated;

    @Value("${spring.cloud.stream.bindings.exportOrderToFulfillmentTools-out-0.destination}")
    private String orderLineExported;

    @Value("${spring.cloud.stream.bindings.transformFulfillmentToolsEvent-out-3.destination}")
    private String orderPartsCancelled;
}
