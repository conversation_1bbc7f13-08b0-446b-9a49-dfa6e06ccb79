package com.bestseller.storeorderfulfillmentservice;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.fulfillmenttoolseventsreceived.FulfillmentToolsEventsReceived;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.UnknownPayload;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.PackJobCreated;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
class FulfillmentToolsEventsReceivedDeserializingTest {

    @Autowired
    private ObjectMapper mapper;

    @Test
    void deserializing_givenKnownPayloadType_noErrorOnDeserialization() throws JsonProcessingException {
        // arrange
        var event = """
            {
                "event": "PACK_JOB_CREATED",
                "eventId": "7344589843917916",
                "payload": {
                    "tenantOrderId": "TB86949290",
                    "version": 1
                }
            }
            """;

        // act
        var eventDeserialized = mapper.readValue(event, FulfillmentToolsEventsReceived.class);

        // assert
        assertThat(eventDeserialized.getPayload()).as("Event type").isInstanceOf(PackJobCreated.class);
    }

    @Test
    void deserializing_givenUnknownPayloadType_noErrorOnDeserialization() throws JsonProcessingException {
        // arrange
        var event = """
            {
                "event": "UNKNOWN_TYPE",
                "eventId": "7344589843917916",
                "payload": {
                    "Boo": "far",
                    "version": 1
                }
            }
            """;

        // act
        var eventDeserialized = mapper.readValue(event, FulfillmentToolsEventsReceived.class);

        // assert
        assertThat(eventDeserialized.getPayload()).as("Event type").isInstanceOf(UnknownPayload.class);
    }

}
