package com.bestseller.storeorderfulfillmentservice.messaging;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.storeorderfulfillmentservice.converter.EcommerceEventConverter;
import com.bestseller.storeorderfulfillmentservice.exception.ImpossibleToPlaceOrderException;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.ecom.EnrichedOrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.OrderCreation;
import com.bestseller.storeorderfulfillmentservice.service.EcommerceEnrichmentService;
import com.bestseller.storeorderfulfillmentservice.service.ValidationService;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.FulfillmentToolsService;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.OrderUploaderService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import util.OrderPartsCreatedGenerator;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@SuppressWarnings("PMD.TooManyMethods")
class ExportOrderToFulfillmentToolsTest {

    @InjectMocks
    private ExportOrderToFulfillmentTools exportOrderToFulfillmentTools;

    @Spy // this class is simple enough not to mock, so we get it covered for free
    private ValidationService validationService = new ValidationService();

    @Mock
    private EcommerceEventConverter ecommerceEventConverter;

    @Mock
    private FulfillmentToolsService fulfillmentToolsService;

    @Mock
    private EcommerceEnrichmentService ecommerceEnrichmentService;

    @Mock
    private OrderUploaderService orderUploaderService;

    @Test
    void apply_givenValidInput_outputIsValid() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        OrderLineExported orderLineExported = new OrderLineExported();
        EnrichedOrderPartsCreated enriched = EnrichedOrderPartsCreated.builder().orderPartsCreated(orderPartsCreated).build();
        when(ecommerceEventConverter.convert(orderPartsCreated)).thenReturn(List.of(orderLineExported));
        when(fulfillmentToolsService.isANewOrder(anyString())).thenReturn(true);
        when(ecommerceEnrichmentService.enrichOrderWithProductInformation(orderPartsCreated)).thenReturn(enriched);
        when(ecommerceEventConverter.convertToOrderCreation(enriched)).thenReturn(OrderCreation.builder().build());

        // act
        var output = exportOrderToFulfillmentTools.apply(Flux.just(orderPartsCreated));
        output.getT2().subscribe();

        // assert
        assertThat(output.getT1().toStream().count()).as("1 item was produced").isEqualTo(1);
        verify(validationService).validate(orderPartsCreated);
    }

    @Test
    void apply_givenShipFromStoreOrderAndNonShipFromStore_onlyShipFromStoreOrderIsProcessed() {
        // arrange
        var notShipFromStoreOrder = OrderPartsCreatedGenerator.generate();
        notShipFromStoreOrder.setFulfillmentNode("NON_SHIP_FROM_STORE_WAREHOUSE");

        var orderPartsCreatedValid = OrderPartsCreatedGenerator.generate();
        OrderLineExported orderLineExported = new OrderLineExported();
        EnrichedOrderPartsCreated enriched = EnrichedOrderPartsCreated.builder().orderPartsCreated(orderPartsCreatedValid).build();
        when(ecommerceEventConverter.convert(orderPartsCreatedValid)).thenReturn(List.of(orderLineExported));
        when(fulfillmentToolsService.isANewOrder(anyString())).thenReturn(true);
        when(ecommerceEnrichmentService.enrichOrderWithProductInformation(orderPartsCreatedValid)).thenReturn(enriched);
        when(ecommerceEventConverter.convertToOrderCreation(enriched)).thenReturn(OrderCreation.builder().build());

        // act
        var output = exportOrderToFulfillmentTools.apply(Flux.just(notShipFromStoreOrder, orderPartsCreatedValid));
        output.getT2().subscribe();

        // assert
        assertThat(output.getT1().toStream().count()).as("1 item was produced").isEqualTo(1);
        verify(validationService).validate(orderPartsCreatedValid);
    }

    @Test
    void apply_givenValidInputButAlreadySentToFulfillmentTools_noMessageProduced() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        when(fulfillmentToolsService.isANewOrder(anyString())).thenReturn(false);

        // act
        var output = exportOrderToFulfillmentTools.apply(Flux.just(orderPartsCreated));
        output.getT2().subscribe();

        // assert
        assertThat(output.getT1().toStream().count()).as("None item was produced").isZero();
        verify(validationService).validate(orderPartsCreated);
    }

    @Test
    void apply_givenErrorBetweenProcesses_oneMessageProduced() {
        // arrange
        var invalidOrderId = "Err404OrderNotFound";

        var validOrderPartsCreated = OrderPartsCreatedGenerator.generate();
        EnrichedOrderPartsCreated validEnriched = EnrichedOrderPartsCreated.builder().orderPartsCreated(validOrderPartsCreated).build();

        var validOrderCreation = OrderCreation.builder().build();

        var invalidOrderPartsCreated = OrderPartsCreatedGenerator.generate();
        invalidOrderPartsCreated.withOrderId(invalidOrderId);
        var invalidOrderCreation = OrderCreation.builder()
            .tenantOrderId(invalidOrderId)
            .build();
        EnrichedOrderPartsCreated invalidEnriched = EnrichedOrderPartsCreated.builder().orderPartsCreated(invalidOrderPartsCreated).build();

        when(ecommerceEventConverter.convert(any())).thenReturn(List.of(new OrderLineExported()));
        when(fulfillmentToolsService.isANewOrder(anyString())).thenReturn(true);
        when(ecommerceEventConverter.convertToOrderCreation(validEnriched)).thenReturn(validOrderCreation);
        when(ecommerceEventConverter.convertToOrderCreation(invalidEnriched)).thenReturn(invalidOrderCreation);
        when(ecommerceEnrichmentService.enrichOrderWithProductInformation(validOrderPartsCreated)).thenReturn(validEnriched);
        when(ecommerceEnrichmentService.enrichOrderWithProductInformation(invalidOrderPartsCreated)).thenReturn(invalidEnriched);

        doThrow(new RuntimeException("An error happened")).when(fulfillmentToolsService).placeAnOrder(invalidOrderCreation);

        // act
        var output = exportOrderToFulfillmentTools.apply(Flux.just(invalidOrderPartsCreated, validOrderPartsCreated, validOrderPartsCreated));
        output.getT2().subscribe();

        // assert
        assertThat(output.getT1().toStream().count()).as("Only two items should be produced").isEqualTo(2);
        verify(validationService, times(2)).validate(validOrderPartsCreated);
        verify(validationService).validate(invalidOrderPartsCreated);
        // Order upload should not happen for invalid order since placement fails
        verify(orderUploaderService, never()).uploadOrder(invalidOrderCreation);
        verify(orderUploaderService, times(2)).uploadOrder(validOrderCreation);
    }

    @Test
    void apply_testOrder_processed() {
        // arrange
        var testOrder = OrderPartsCreatedGenerator.generate();
        testOrder.setOrderId("test");
        testOrder.getCustomerInformation().setEmail("<EMAIL>");

        var realOrder = OrderPartsCreatedGenerator.generate();
        realOrder.setOrderId("real");

        // act
        var output = exportOrderToFulfillmentTools.apply(Flux.just(realOrder, testOrder));
        output.getT2().subscribe();

        // assert
        output.getT1().ignoreElements().block();
        verify(fulfillmentToolsService).isANewOrder("test");
    }

    @Test
    void apply_testOrder_processed2() {
        // arrange
        var orderPartsCreatedValid = OrderPartsCreatedGenerator.generate();
        orderPartsCreatedValid.getCustomerInformation().setEmail("<EMAIL>");

        // act
        var output = exportOrderToFulfillmentTools.apply(Flux.just(orderPartsCreatedValid));
        output.getT2().subscribe();

        // assert
        output.getT1().ignoreElements().block();
        verify(fulfillmentToolsService).isANewOrder(orderPartsCreatedValid.getOrderId());
    }

    @Test
    void apply_givenException_orderPartsRejectedIsProduced() {
        // arrange
        var orderPartsCreatedValid = OrderPartsCreatedGenerator.generate();
        orderPartsCreatedValid.setFulfillmentNode("SHIP_FROM_STORE_DE");

        EnrichedOrderPartsCreated enrichedOrderPartsCreated = EnrichedOrderPartsCreated.builder()
            .orderPartsCreated(orderPartsCreatedValid)
            .build();
        when(fulfillmentToolsService.isANewOrder(anyString())).thenReturn(true);
        when(ecommerceEnrichmentService.enrichOrderWithProductInformation(orderPartsCreatedValid)).thenReturn(enrichedOrderPartsCreated);
        when(ecommerceEventConverter.convertToOrderCreation(any()))
            .thenThrow(new ImpossibleToPlaceOrderException("Impossible to place"));
        OrderPartRejected orderPartRejected = new OrderPartRejected();
        when(ecommerceEventConverter.rejectOrder(enrichedOrderPartsCreated)).thenReturn(orderPartRejected);

        // act
        var output = exportOrderToFulfillmentTools.apply(Flux.just(orderPartsCreatedValid));
        output.getT1().subscribe();

        // assert
        assertThat(output.getT2().toStream().toList())
            .hasSize(1)
            .first()
            .isEqualTo(orderPartRejected);

    }

    @Test
    void apply_givenValidInput_shouldUploadOrderBeforePlacing() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        OrderCreation orderCreation = OrderCreation.builder().tenantOrderId("test-order").build();
        EnrichedOrderPartsCreated enriched = EnrichedOrderPartsCreated.builder()
            .orderPartsCreated(orderPartsCreated)
            .orderCreation(null) // Set to null initially as it will be set by the convertToOrderCreation mock
            .build();

        when(ecommerceEventConverter.convert(orderPartsCreated)).thenReturn(List.of(new OrderLineExported()));
        when(fulfillmentToolsService.isANewOrder(anyString())).thenReturn(true);
        when(ecommerceEnrichmentService.enrichOrderWithProductInformation(orderPartsCreated)).thenReturn(enriched);
        when(ecommerceEventConverter.convertToOrderCreation(enriched)).thenReturn(orderCreation);

        // act
        var output = exportOrderToFulfillmentTools.apply(Flux.just(orderPartsCreated));
        output.getT2().subscribe();
        output.getT1().subscribe();

        // assert
        verify(orderUploaderService).uploadOrder(orderCreation);
        verify(fulfillmentToolsService).placeAnOrder(orderCreation);
    }
}
