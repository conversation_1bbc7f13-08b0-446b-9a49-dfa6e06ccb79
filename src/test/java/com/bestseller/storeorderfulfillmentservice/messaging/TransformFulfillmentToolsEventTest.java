package com.bestseller.storeorderfulfillmentservice.messaging;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.fulfillmenttoolseventsreceived.FulfillmentToolsEventsReceived;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.Payload;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.dispatched.HandoverJobHandedOver;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.notroutable.RoutingPlanNotRoutable;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobAborted;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobCreated;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobPickingFinished;
import com.bestseller.storeorderfulfillmentservice.converter.FulfillmentEventConverter;
import com.bestseller.storeorderfulfillmentservice.converter.OrderDetailsConverter;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedHandoverJobHandedOver;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedPickJobAborted;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedPickJobCreated;
import com.bestseller.storeorderfulfillmentservice.model.order.OrderDetails;
import com.bestseller.storeorderfulfillmentservice.model.pcs.EnrichedPickJobPickingFinished;
import com.bestseller.storeorderfulfillmentservice.service.FulfillmentEventEnrichmentService;
import com.bestseller.storeorderfulfillmentservice.service.ValidationService;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.DocumentService;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.EventUploaderService;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import util.HandoverJobHandedOverPayloadGenerator;
import util.PackJobCreatedPayloadGenerator;
import util.PickJobAbortedPayloadGenerator;
import util.PickJobCreatedPayloadGenerator;
import util.PickJobPickingFinishedPayloadGenerator;
import util.RoutingPlanNotRoutablePayloadGenerator;

import javax.validation.ValidationException;
import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TransformFulfillmentToolsEventTest {

    private static final String EVENT_ID = "9285D8A2-37E0-4C4C-90ED-2B0C871F7950";
    private final EnrichedPickJobCreated richPickJobCreated = new EnrichedPickJobCreated(mock(PickJobCreated.class), null);
    private final EnrichedPickJobPickingFinished richPickJobPickingFinished =
        new EnrichedPickJobPickingFinished(mock(PickJobPickingFinished.class), null);
    private final EnrichedHandoverJobHandedOver richHandover = new EnrichedHandoverJobHandedOver(
        mock(HandoverJobHandedOver.class),
        null,
        null,
        null,
        null);

    @InjectMocks
    private TransformFulfillmentToolsEvent transformFulfillmentToolsEvent;

    @Spy // this class is simple enough not to mock, so we get it covered for free
    private ValidationService validationService = new ValidationService();

    @Mock
    private FulfillmentEventEnrichmentService fulfillmentEventEnrichmentService;

    @Mock
    private FulfillmentEventConverter fulfillmentEventConverter;

    @Mock
    private DocumentService documentService;

    @Mock
    private OrderDetailsConverter orderDetailsConverter;

    @Mock
    private EventUploaderService eventUploaderService;

    @Test
    void transformFulfillmentEvent_pickJobCreated_orderLineAcknowledgedProduced() {
        // arrange
        final int orderLines = 42;
        var payload = PickJobCreatedPayloadGenerator.generate();
        var fulfillmentEvent = wrapInFulfillmentToolsEventsReceived(payload);

        when(fulfillmentEventEnrichmentService.enrichWithFacility(any(PickJobCreated.class)))
            .thenReturn(richPickJobCreated);

        when(fulfillmentEventConverter.convert(richPickJobCreated))
            .thenReturn(Collections.nCopies(orderLines, new OrderLineAcknowledged()));

        // act
        var outputs = transformFulfillmentToolsEvent
            .apply(Flux.just(fulfillmentEvent));
        Flux<OrderLineAcknowledged> ecomEvents = outputs.getT1();
        outputs.getT2().subscribe();
        outputs.getT3().subscribe();
        outputs.getT4().subscribe();

        // assert
        assertThat(ecomEvents.toStream().count())
            .as("# OrderLineAcknowledged messages produced")
            .isEqualTo(orderLines);
        verify(validationService).validate(fulfillmentEvent);
        // Verify that the event is uploaded
        verify(eventUploaderService).uploadEvent(fulfillmentEvent);
    }

    @Test
    void transformFulfillmentEvent_handoverJobHandedOver_orderLineDispatchedProduced() {
        // arrange
        final int orderLines = 69;
        var payload = HandoverJobHandedOverPayloadGenerator.generate();
        var fulfillmentEvent = wrapInFulfillmentToolsEventsReceived(payload);

        when(fulfillmentEventEnrichmentService.enrichWithFacilityAndParcel(any(HandoverJobHandedOver.class)))
            .thenReturn(richHandover);

        when(fulfillmentEventConverter.convert(richHandover))
            .thenReturn(Collections.nCopies(orderLines, new OrderLineDispatched()));

        // act
        var outputs = transformFulfillmentToolsEvent.apply(Flux.just(fulfillmentEvent));
        outputs.getT1().subscribe();
        Flux<OrderLineDispatched> ecomEvents = outputs.getT2();
        outputs.getT3().subscribe();
        outputs.getT4().subscribe();

        // assert
        assertThat(ecomEvents.toStream().count())
            .as("# OrderLineDispatched messages produced")
            .isEqualTo(orderLines);
        verify(validationService).validate(fulfillmentEvent);
        // Verify that the event is uploaded
        verify(eventUploaderService).uploadEvent(fulfillmentEvent);
    }

    @Test
    void transformFulfillmentEvent_routingPlanNotRoutable_orderPartRejectedProduced() {
        // arrange
        final int orderParts = 69;
        var payload = RoutingPlanNotRoutablePayloadGenerator.generate().build();
        var fulfillmentEvent = wrapInFulfillmentToolsEventsReceived(payload);

        when(fulfillmentEventConverter.convert(payload))
            .thenReturn(new OrderPartRejected());

        // act
        var outputs = transformFulfillmentToolsEvent
            .apply(Flux.fromIterable(Collections.nCopies(orderParts, fulfillmentEvent)));
        outputs.getT1().subscribe();
        outputs.getT2().subscribe();
        Flux<OrderPartRejected> ecomEvents = outputs.getT3();
        outputs.getT4().subscribe();

        // assert
        assertThat(ecomEvents.toStream().count())
            .as("#OrderPartRejected messages produced")
            .isEqualTo(orderParts);
        verify(validationService, times(orderParts)).validate(fulfillmentEvent);
        // Verify that the event is uploaded exactly orderParts times
        verify(eventUploaderService, times(orderParts)).uploadEvent(fulfillmentEvent);
    }

    @Test
    void transformFulfillmentEvent_pickJobAbortedVariousAge_orderPartRejectedProducedForRecentOrders() {
        // arrange
        Instant orderDate = Instant.parse("2023-01-01T01:01:01.001Z");
        Instant expirationDate = Instant.parse("2023-11-11T11:11:11.111Z");
        transformFulfillmentToolsEvent.setReroutePeriod(Duration.between(orderDate, expirationDate));

        var expiredJob = PickJobAbortedPayloadGenerator.generate()
            .orderDate(orderDate)
            .created(expirationDate.plusMillis(1))
            .build();

        var retriableJob = PickJobAbortedPayloadGenerator.generate()
            .orderDate(orderDate)
            .created(expirationDate.minusMillis(1))
            .build();

        when(fulfillmentEventEnrichmentService.enrichWithFacility(any(PickJobAborted.class)))
            .thenReturn(new EnrichedPickJobAborted(null, null));

        when(fulfillmentEventConverter.convert(any(EnrichedPickJobAborted.class)))
            .thenReturn(new OrderPartRejected());

        // act
        var outputs = transformFulfillmentToolsEvent.apply(Flux.just(
            wrapInFulfillmentToolsEventsReceived(expiredJob),
            wrapInFulfillmentToolsEventsReceived(retriableJob)));
        outputs.getT1().subscribe();
        outputs.getT2().subscribe();
        Flux<OrderPartRejected> ecomEvents = outputs.getT3();
        outputs.getT4().subscribe();

        // assert
        assertThat(ecomEvents.toStream().count())
            .as("# rejection msg produced")
            .isEqualTo(1);
        verify(fulfillmentEventEnrichmentService).enrichWithFacility(expiredJob);
        verify(fulfillmentEventEnrichmentService, never()).enrichWithFacility(retriableJob);
    }

    @SneakyThrows
    private static FulfillmentToolsEventsReceived wrapInFulfillmentToolsEventsReceived(Payload expiredJob) {
        String eventName = (String) expiredJob.getClass().getField("NAME").get(expiredJob);
        return new FulfillmentToolsEventsReceived()
            .withEvent(eventName)
            .withEventId(EVENT_ID)
            .withPayload(expiredJob);
    }

    @Test
    void transformFulfillmentEvent_validationError_nextMessageProcessed() {
        // arrange
        var payload = PickJobCreated.builder().build();
        var ecomEvent = new OrderLineAcknowledged();

        Flux<FulfillmentToolsEventsReceived> fulfillmentEvents = Flux.just(payload, payload)
            .map(payload1 -> wrapInFulfillmentToolsEventsReceived(payload1));

        doThrow(ValidationException.class).
            doNothing()
            .when(validationService).validate(any());

        when(fulfillmentEventEnrichmentService.enrichWithFacility(payload)).thenReturn(richPickJobCreated);

        when(fulfillmentEventConverter.convert(richPickJobCreated)).thenReturn(List.of(ecomEvent));

        // act
        var outputs = transformFulfillmentToolsEvent.apply(fulfillmentEvents);

        // assert
        assertThat(Flux.merge(outputs.getT1(), outputs.getT2(), outputs.getT3(), outputs.getT4()).toStream().toList())
            .as("E-commerce events")
            .containsExactly(ecomEvent);
    }

    @Test
    void transformFulfillmentEvent_enrichmentErrors_nextMessageProcessed() {
        // arrange
        var pickJob = PickJobCreated.builder().build();
        var handover = HandoverJobHandedOver.builder().build();
        var unroutable = RoutingPlanNotRoutable.builder().build();

        Flux<FulfillmentToolsEventsReceived> fulfillmentEvents = Flux.just(
            pickJob, // will fail
            handover, // will fail
            pickJob, // will succeed
            unroutable, // will fail
            handover, // will succeed
            unroutable // will succeed
        ).map(payload -> wrapInFulfillmentToolsEventsReceived(payload));

        var orderLineAcknowledged = new OrderLineAcknowledged();
        var orderLineDispatched = new OrderLineDispatched();
        var orderPartRejected = new OrderPartRejected();

        // enrichment fails then succeeds
        when(fulfillmentEventEnrichmentService.enrichWithFacility(any(PickJobCreated.class)))
            .thenThrow(RuntimeException.class)
            .thenReturn(richPickJobCreated);

        when(fulfillmentEventEnrichmentService.enrichWithFacilityAndParcel(any(HandoverJobHandedOver.class)))
            .thenThrow(RuntimeException.class)
            .thenReturn(richHandover);

        // conversion
        when(fulfillmentEventConverter.convert(richPickJobCreated))
            .thenReturn(List.of(orderLineAcknowledged));

        when(fulfillmentEventConverter.convert(richHandover))
            .thenReturn(List.of(orderLineDispatched));

        when(fulfillmentEventConverter.convert(unroutable))
            .thenThrow(RuntimeException.class)
            .thenReturn(orderPartRejected);

        // act
        var outputs = transformFulfillmentToolsEvent.apply(fulfillmentEvents);

        assertThat(Flux.merge(outputs.getT1(), outputs.getT2(), outputs.getT3(), outputs.getT4()).toStream().toList())
            .as("E-commerce events")
            .containsExactly(orderLineAcknowledged, orderLineDispatched, orderPartRejected);
    }

    @Test
    void transformFulfillmentEvent_packJobCreated_documentServiceWasCalled() {
        // arrange
        var payload = PackJobCreatedPayloadGenerator.generate();
        var orderDetails = OrderDetails.builder().build();
        when(orderDetailsConverter.convert(payload))
            .thenReturn(orderDetails);
        var fulfillmentEvent = wrapInFulfillmentToolsEventsReceived(payload);

        // act
        var outputs = transformFulfillmentToolsEvent.apply(Flux.just(fulfillmentEvent));
        outputs.getT1().subscribe();
        outputs.getT2().subscribe();
        outputs.getT3().subscribe();
        outputs.getT4().subscribe();

        // assert
        verify(fulfillmentEventEnrichmentService).enrichWithFacilityDetails(orderDetails);
        verify(fulfillmentEventEnrichmentService).enrichWithPrice(orderDetails);
        verify(validationService).validate(fulfillmentEvent);
        verify(documentService).attachDocuments(orderDetails);
        verify(eventUploaderService).uploadEvent(fulfillmentEvent);
    }

    @Test
    void transformFulfillmentEvent_pickJobPickingFinished_orderPartCancelledProduced() {
        // arrange
        final int orderParts = 69;
        var payload = PickJobPickingFinishedPayloadGenerator.generate();
        var fulfillmentEvent = new FulfillmentToolsEventsReceived()
            .withEvent(PickJobPickingFinished.NAME)
            .withEventId(EVENT_ID)
            .withPayload(payload);

        when(fulfillmentEventEnrichmentService.enrichWithFacility(any(PickJobPickingFinished.class)))
            .thenReturn(richPickJobPickingFinished);

        when(fulfillmentEventConverter.convert(any(EnrichedPickJobPickingFinished.class)))
            .thenReturn(Optional.of(new OrderPartsCancelled()));

        // act
        var outputs = transformFulfillmentToolsEvent
            .apply(Flux.fromIterable(Collections.nCopies(orderParts, fulfillmentEvent)));
        outputs.getT1().subscribe();
        outputs.getT2().subscribe();
        outputs.getT3().subscribe();
        Flux<OrderPartsCancelled> ecomEvents = outputs.getT4();

        // assert
        assertThat(ecomEvents.toStream().count())
            .as("# msg produced")
            .isEqualTo(orderParts);
        verify(validationService, times(orderParts)).validate(fulfillmentEvent);
        verify(eventUploaderService, times(orderParts)).uploadEvent(fulfillmentEvent);
    }
}
