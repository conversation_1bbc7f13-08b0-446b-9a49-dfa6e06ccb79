package com.bestseller.storeorderfulfillmentservice.controller;

import com.bestseller.storeorderfulfillmentservice.configuration.localization.LocalizationConfig;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.DocumentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicBoolean;

import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class DocumentControllerTest {

    @Mock
    private DocumentService documentService;

    @InjectMocks
    private DocumentController documentController;

    @Mock
    private LocalizationConfig localizationConfig;

    private MockMvc mvc;

    private AtomicBoolean streamingFinished = new AtomicBoolean();

    @BeforeEach
    void initializeMockMvc() {
        mvc = MockMvcBuilders.standaloneSetup(documentController).build();
    }

    @Test
    void renderDocument_jsonSent_pdfReturned() throws Exception {
        // arrange
        byte[] bytes = "%PDF-invoice-return-label".getBytes(StandardCharsets.UTF_8);

        doAnswer(invocation -> {
            OutputStream out = invocation.getArgument(1);
            out.write(bytes);
            streamingFinished.set(true);
            return null;
        }).when(documentService).generateDocument(any(), any());

        // act
        ResultActions result = mvc.perform(post("/support/document/render")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"));

        await("streaming finished").until(() -> streamingFinished, AtomicBoolean::get);

        // assert
        result.andExpect(status().isOk())
                .andExpect(content().bytes(bytes));
    }

}
