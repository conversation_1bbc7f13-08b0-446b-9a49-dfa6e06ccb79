package com.bestseller.storeorderfulfillmentservice.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.notroutable.RoutingPlanNotRoutable;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobAborted;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedPickJobAborted;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.CancellationReason;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.facility.Facility;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.facility.FacilityAddress;
import com.bestseller.storeorderfulfillmentservice.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import util.OrderCancelledPayloadGenerator;
import util.OrderPartsCreatedGenerator;
import util.PickJobAbortedPayloadGenerator;
import util.RoutingPlanNotRoutablePayloadGenerator;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class OrderPartsRejectedConverterTest {

    private static final String SHIP_FROM_STORE_PREFIX = "SHIP_FROM_STORE_";
    private static final String COUNTRY_NETHERLANDS = "NL";
    private final Clock clock = Clock.fixed(Instant.parse("2024-01-30T10:15:30.00Z"), ZoneId.of("UTC"));
    private OrderPartsRejectedConverter converter;

    @BeforeEach
    void setUp() {
        converter = new OrderPartsRejectedConverter(clock, new DateUtils(clock));
    }

    @Test
    void convert_givenOrderCancelled_returnsOrderPartsRejected() {
        // arrange
        var orderCancelled = OrderCancelledPayloadGenerator.generate()
            .build();

        // act
        var orderPartsRejected = converter.convert(orderCancelled);

        OrderPartRejected expectedOrderPartRejected = new OrderPartRejected()
            .withOrderId(OrderCancelledPayloadGenerator.ORDER_ID)
            .withCancellationDate(ZonedDateTime.parse("2023-02-01T10:45:51.525+01:00[UTC]"))
            .withWarehouse(SHIP_FROM_STORE_PREFIX.concat(OrderCancelledPayloadGenerator.COUNTRY))
            .withIsTest(false)
            .withOrderLines(List.of(
                new OrderLine()
                    .withCancelReason(CancellationReason.ITEM_NOT_AVAILABLE.name())
                    .withEan(OrderCancelledPayloadGenerator.EAN_1)
                    .withLineNumber(1)
                    .withQuantity(OrderCancelledPayloadGenerator.QUANTITY_EAN_1),
                new OrderLine()
                    .withCancelReason(CancellationReason.ITEM_NOT_AVAILABLE.name())
                    .withEan(OrderCancelledPayloadGenerator.EAN_2)
                    .withLineNumber(2)
                    .withQuantity(OrderCancelledPayloadGenerator.QUANTITY_EAN_2)
            ));
        // assert
        assertThat(orderPartsRejected)
            .usingRecursiveComparison()
            .isEqualTo(expectedOrderPartRejected);
    }

    @Test
    void convert_givenEnrichedPickJobAborted_returnsOrderPartsRejected() {
        // arrange
        String orderId = "OL122786768";
        PickJobAborted pickJobAborted = PickJobAbortedPayloadGenerator.generate()
            .tenantOrderId(orderId)
            .build();
        EnrichedPickJobAborted enrichedPickJobAborted = new EnrichedPickJobAborted(
            pickJobAborted,
            new Facility(
                "123",
                FacilityAddress.builder()
                    .country(COUNTRY_NETHERLANDS)
                    .build()));
        // act
        var orderPartsRejected = converter.convert(enrichedPickJobAborted);

        OrderPartRejected expectedOrderPartRejected = new OrderPartRejected()
            .withOrderId(orderId)
            .withCancellationDate(ZonedDateTime.parse("2023-02-01T10:45:51.525+01:00[UTC]"))
            .withWarehouse(SHIP_FROM_STORE_PREFIX.concat(OrderCancelledPayloadGenerator.COUNTRY))
            .withIsTest(false)
            .withOrderLines(List.of(
                new OrderLine()
                    .withCancelReason(CancellationReason.ITEM_NOT_AVAILABLE.name())
                    .withEan(OrderCancelledPayloadGenerator.EAN_1)
                    .withLineNumber(1)
                    .withQuantity(OrderCancelledPayloadGenerator.QUANTITY_EAN_1),
                new OrderLine()
                    .withCancelReason(CancellationReason.ITEM_NOT_AVAILABLE.name())
                    .withEan(OrderCancelledPayloadGenerator.EAN_2)
                    .withLineNumber(2)
                    .withQuantity(OrderCancelledPayloadGenerator.QUANTITY_EAN_2)
            ));
        // assert
        assertThat(orderPartsRejected)
            .usingRecursiveComparison()
            .isEqualTo(expectedOrderPartRejected);
    }

    @Test
    void convert_givenRoutingPlanNotRoutable_returnsOrderPartsRejected() {
        // arrange
        String orderId = "OL187638120";
        RoutingPlanNotRoutable routingPlanNotRoutable = RoutingPlanNotRoutablePayloadGenerator.generate()
            .tenantOrderId(orderId)
            .targetAddress(com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.FacilityAddress.builder()
                .country(COUNTRY_NETHERLANDS)
                .build())
            .build();

        // act
        var orderPartsRejected = converter.convert(routingPlanNotRoutable);

        OrderPartRejected expectedOrderPartRejected = new OrderPartRejected()
            .withOrderId(orderId)
            .withCancellationDate(ZonedDateTime.parse("2023-02-03T10:45:51.525+01:00[UTC]"))
            .withWarehouse(SHIP_FROM_STORE_PREFIX.concat(OrderCancelledPayloadGenerator.COUNTRY))
            .withIsTest(false)
            .withOrderLines(List.of(
                new OrderLine()
                    .withCancelReason(CancellationReason.ITEM_NOT_AVAILABLE.name())
                    .withEan(RoutingPlanNotRoutablePayloadGenerator.ARTICLE_ID)
                    .withLineNumber(1)
                    .withQuantity(1),
                new OrderLine()
                    .withCancelReason(CancellationReason.ITEM_NOT_AVAILABLE.name())
                    .withEan(RoutingPlanNotRoutablePayloadGenerator.ARTICLE_ID)
                    .withLineNumber(2)
                    .withQuantity(1))
            );
        // assert
        assertThat(orderPartsRejected)
            .usingRecursiveComparison()
            .isEqualTo(expectedOrderPartRejected);
    }

    @Test
    void convert_givenOrderPartsCreated_returnsOrderPartsRejected() {
        // arrange
        OrderPartsCreated orderPartsCreated = OrderPartsCreatedGenerator.generate();

        // act
        var orderPartsRejected = converter.convert(orderPartsCreated);

        OrderPartRejected expectedOrderPartRejected = new OrderPartRejected()
            .withOrderId(OrderPartsCreatedGenerator.ORDER_ID)
            .withCancellationDate(ZonedDateTime.parse("2024-01-30T11:15:30+01:00[UTC]"))
            .withWarehouse(orderPartsCreated.getFulfillmentNode())
            .withIsTest(false)
            .withOrderLines(List.of(
                new OrderLine()
                    .withCancelReason(CancellationReason.ITEM_NOT_AVAILABLE.name())
                    .withEan(OrderPartsCreatedGenerator.EAN_1)
                    .withLineNumber(1)
                    .withQuantity(OrderPartsCreatedGenerator.QUANTITY_1),
                new OrderLine()
                    .withCancelReason(CancellationReason.ITEM_NOT_AVAILABLE.name())
                    .withEan(OrderPartsCreatedGenerator.EAN_2)
                    .withLineNumber(2)
                    .withQuantity(OrderPartsCreatedGenerator.QUANTITY_2))
            );
        // assert
        assertThat(orderPartsRejected)
            .usingRecursiveComparison()
            .isEqualTo(expectedOrderPartRejected);
    }
}
