package com.bestseller.storeorderfulfillmentservice.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.Article;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.LineItem;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.dispatched.HandoverJobParcelInfo;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedHandoverJobHandedOver;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedPickJobAborted;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedPickJobCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.Parcel;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.ParcelInformation;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.carrier.Carrier;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.facility.Facility;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.facility.FacilityAddress;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.picked.PickJob;
import com.bestseller.storeorderfulfillmentservice.model.pcs.EnrichedPickJobPickingFinished;
import com.bestseller.storeorderfulfillmentservice.utils.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import util.HandoverJobHandedOverPayloadGenerator;
import util.OrderCancelledPayloadGenerator;
import util.PickJobCreatedPayloadGenerator;
import util.PickJobPickingFinishedPayloadGenerator;
import util.RoutingPlanNotRoutablePayloadGenerator;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class FulfillmentEventConverterTest {

    public static final String EAN = "*************";
    private static final int QUANTITY = 42;
    private static final int PICKED = 42;
    private static final String COUNTRY = "AB";
    private static final String WAREHOUSE = "SHIP_FROM_STORE_AB";
    private static final int LOCATION_CHAIN_CODE = 123_123;
    private static final String RETURN_LABEL = "ABC 123 4-56";

    private final ZoneId timeZone = ZoneId.of("America/Argentina/Buenos_Aires");

    private final Clock clock = Clock.fixed(Instant.EPOCH, timeZone);

    @Mock
    private OrderPartsRejectedConverter orderPartsRejectedConverter;

    private FulfillmentEventConverter converter;

    @BeforeEach
    void setUp() {
        converter = new FulfillmentEventConverter(
            orderPartsRejectedConverter,
            new DateUtils(clock));
    }

    @Test
    void convert_givenValidPickJobCreatedPayload_returnValidOrderLineAchknowledgedEvent() {
        //arrange
        var orderCreated = PickJobCreatedPayloadGenerator.generate();
        Facility facility = new Facility(String.valueOf(LOCATION_CHAIN_CODE), FacilityAddress.builder()
            .country(COUNTRY).build());

        //act
        var orderLineAcknowledgedList = converter.convert(new EnrichedPickJobCreated(orderCreated, facility));
        var orderLineAcknowledged = orderLineAcknowledgedList.iterator().next();

        //assert
        assertThat(orderLineAcknowledgedList).as("The list is not null").isNotNull();
        assertThat(orderLineAcknowledgedList).as("The list is not empty").isNotEmpty();
        assertThat(orderLineAcknowledgedList).as("There is only 1 OLA event created").hasSize(1);
        assertThat(orderLineAcknowledged.getOrderId()).as("Order id matches").isEqualTo(orderCreated.getTenantOrderId());
        assertThat(orderLineAcknowledged.getAcknowledgementDate()).as("Acknowledged date matches")
            .isEqualTo(ZonedDateTime.ofInstant(orderCreated.getCreated(), timeZone));
        assertThat(orderLineAcknowledged.getEan()).as("EAN matches")
            .isEqualTo(orderCreated.getPickLineItems().get(0).getArticle().getEan());
        assertThat(orderLineAcknowledged.getQuantity()).as("Quantity matches")
            .isEqualTo(orderCreated.getPickLineItems().get(0).getQuantity());
        assertThat(orderLineAcknowledged.getWarehouse()).as("Warehouse matches").isEqualTo(WAREHOUSE);
        assertThat(orderLineAcknowledged.getStoreId()).as("Store id matches").isEqualTo(LOCATION_CHAIN_CODE);
    }

    @Test
    void convert_givenValidHandoverJobHandedOver_returnsValidOrderLineDispatchedEvent() {
        //arrange
        var handoverJob = HandoverJobHandedOverPayloadGenerator.generate();
        Facility facility = new Facility(String.valueOf(LOCATION_CHAIN_CODE), FacilityAddress.builder()
            .country(COUNTRY)
            .build());
        Parcel parcel = new Parcel(new ParcelInformation(RETURN_LABEL));

        PickJob pickJob = PickJob.builder()
            .pickLineItems(List.of(LineItem.builder()
                .article(new Article(EAN))
                .quantity(QUANTITY)
                .picked(PICKED)
                .build()))
            .build();
        Carrier carrier = new Carrier("DHL");

        //act
        var orderLineShippedList = converter.convert(new EnrichedHandoverJobHandedOver(
            handoverJob,
            facility,
            parcel,
            pickJob,
            carrier));

        var orderLineShipped = orderLineShippedList.iterator().next();

        //assert
        assertThat(orderLineShippedList).as("The list is not null").isNotNull();
        assertThat(orderLineShippedList).as("The list is not empty").isNotEmpty();
        assertThat(orderLineShippedList).as("There is only 1 OLS event created").hasSize(1);
        assertThat(orderLineShipped.getOrderId()).as("Order id matches").isEqualTo(handoverJob.getTenantOrderId());
        assertThat(orderLineShipped.getDispatchDate()).as("Dispatch date matches")
            .isEqualTo(ZonedDateTime.ofInstant(handoverJob.getCreated(), timeZone));
        assertThat(orderLineShipped.getOrangePrinted()).as("Orange Printed date matches")
            .isEqualTo(ZonedDateTime.ofInstant(handoverJob.getCreated(), timeZone));
        assertThat(orderLineShipped.getCarrierName()).as("Carrier name matches").isEqualTo(carrier.name());
        assertThat(orderLineShipped.getTrackingNumber()).as("Tracking number matches").isEqualTo(handoverJob.getCarrierTrackingNumber());
        assertThat(orderLineShipped.getWarehouse()).as("Warehouse is the correct one").isEqualTo(WAREHOUSE);
        assertThat(orderLineShipped.getStoreId()).as("Store id matches").isEqualTo(LOCATION_CHAIN_CODE);
        assertThat(orderLineShipped.getReturnShipmentId()).as("Return shipment id matches").isEqualTo(RETURN_LABEL);
        //order line
        assertThat(orderLineShipped.getQuantity()).as("quantity").isEqualTo(PICKED);
        assertThat(orderLineShipped.getEan()).as("EAN").isEqualTo(EAN);
    }

    @Test
    void convert_givenCarrierIsBring_returnsValidOrderLineDispatchedEventWithCorrectTrackingNumber() {
        //arrange
        String carrierParcelRef = "370722151955320794";

        var handoverJob = HandoverJobHandedOverPayloadGenerator.generate();
        handoverJob.setHandoverJobParcelInfo(new HandoverJobParcelInfo(carrierParcelRef));

        Facility facility = new Facility(String.valueOf(LOCATION_CHAIN_CODE), FacilityAddress.builder()
            .country(COUNTRY)
            .build());
        Parcel parcel = new Parcel(new ParcelInformation(RETURN_LABEL));

        PickJob pickJob = PickJob.builder()
            .pickLineItems(List.of(LineItem.builder()
                .article(new Article(EAN))
                .quantity(QUANTITY)
                .picked(PICKED)
                .build()))
            .build();
        Carrier carrier = new Carrier("BRING");

        //act
        var orderLineShippedList = converter.convert(new EnrichedHandoverJobHandedOver(
            handoverJob,
            facility,
            parcel,
            pickJob,
            carrier));

        var orderLineShipped = orderLineShippedList.iterator().next();

        //assert
        assertThat(orderLineShippedList).as("The list is not null").isNotNull();
        assertThat(orderLineShippedList).as("The list is not empty").isNotEmpty();
        assertThat(orderLineShippedList).as("There is only 1 OLS event created").hasSize(1);
        assertThat(orderLineShipped.getOrderId()).as("Order id matches").isEqualTo(handoverJob.getTenantOrderId());
        assertThat(orderLineShipped.getDispatchDate()).as("Dispatch date matches")
            .isEqualTo(ZonedDateTime.ofInstant(handoverJob.getCreated(), timeZone));
        assertThat(orderLineShipped.getOrangePrinted()).as("Orange Printed date matches")
            .isEqualTo(ZonedDateTime.ofInstant(handoverJob.getCreated(), timeZone));
        assertThat(orderLineShipped.getCarrierName()).as("Carrier name matches").isEqualTo(carrier.name());
        assertThat(orderLineShipped.getTrackingNumber()).as("Tracking number matches").isEqualTo(carrierParcelRef);
        assertThat(orderLineShipped.getWarehouse()).as("Warehouse is the correct one").isEqualTo(WAREHOUSE);
        assertThat(orderLineShipped.getStoreId()).as("Store id matches").isEqualTo(LOCATION_CHAIN_CODE);
        assertThat(orderLineShipped.getReturnShipmentId()).as("Return shipment id matches").isEqualTo(RETURN_LABEL);
        //order line
        assertThat(orderLineShipped.getQuantity()).as("quantity").isEqualTo(PICKED);
        assertThat(orderLineShipped.getEan()).as("EAN").isEqualTo(EAN);
    }


    /**
     * Complementary scenario for convert_givenValidHandoverJobHandedOver_returnsValidOrderLineDispatchedEvent.
     */
    @Test
    void convert_givenValidHandoverJobHandedOverWithoutTrackingNumber_returnsValidOrderLineDispatchedEvent() {
        //arrange
        var handoverJob = HandoverJobHandedOverPayloadGenerator.generate();
        handoverJob.setCarrierTrackingNumber(null);

        Facility facility = new Facility(String.valueOf(LOCATION_CHAIN_CODE), FacilityAddress.builder()
            .country(COUNTRY)
            .build());
        Parcel parcel = new Parcel(new ParcelInformation(RETURN_LABEL));

        PickJob pickJob = PickJob.builder()
            .pickLineItems(List.of(LineItem.builder()
                .article(new Article(EAN))
                .quantity(QUANTITY)
                .picked(PICKED)
                .build()))
            .build();
        Carrier carrier = new Carrier("DHL");

        //act
        var orderLineShippedList = converter.convert(new EnrichedHandoverJobHandedOver(
            handoverJob,
            facility,
            parcel,
            pickJob,
            carrier));

        var orderLineShipped = orderLineShippedList.iterator().next();

        //assert
        assertThat(orderLineShipped.getTrackingNumber()).as("Tracking number is null").isNull();
    }

    @Test
    void convert_givenValidHandoverJobHandedOverWithZeroQuantity_noOrderLineDispatchedIsReturned() {
        //arrange
        var picked = 0;
        var handoverJob = HandoverJobHandedOverPayloadGenerator.generate();
        Facility facility = new Facility(String.valueOf(LOCATION_CHAIN_CODE), FacilityAddress.builder()
            .country(COUNTRY)
            .build());
        Parcel parcel = new Parcel(new ParcelInformation(RETURN_LABEL));

        PickJob pickJob = PickJob.builder()
            .pickLineItems(List.of(LineItem.builder()
                .article(new Article(EAN))
                .quantity(QUANTITY)
                .picked(picked)
                .build()))
            .build();
        Carrier carrier = new Carrier("DHL");

        //act
        var orderLineShippedList = converter.convert(new EnrichedHandoverJobHandedOver(
            handoverJob,
            facility,
            parcel,
            pickJob,
            carrier));


        //assert
        assertThat(orderLineShippedList).as("The list is empty").isEmpty();
    }

    @Test
    void convert_givenRoutingPlanNotRoutable_conversionHasBeenDelegated() {
        // arrange
        var routingJob = RoutingPlanNotRoutablePayloadGenerator.generate()
            .build();

        // act & assert
        converter.convert(routingJob);
        verify(orderPartsRejectedConverter).convert(routingJob);
    }

    @Test
    void convert_givenPickingJobPickingFinished_returnsOrderPartsCancelledWithOneOrderLine() {
        String ean = "IAMCANCELLED";

        var pickJobPicking = PickJobPickingFinishedPayloadGenerator.generate();

        Facility facility = new Facility(String.valueOf(LOCATION_CHAIN_CODE), FacilityAddress.builder()
            .country(COUNTRY)
            .build());

        var pickedOrderLine = LineItem.builder()
            .article(PickJobPickingFinishedPayloadGenerator.generateArticle())
            .quantity(1)
            .picked(1)
            .build();

        var cancelledOrderLine = LineItem.builder()
            .article(new Article(ean))
            .quantity(1)
            .picked(0)
            .build();

        var orderLines = List.of(pickedOrderLine, cancelledOrderLine);
        pickJobPicking.setPickLineItems(orderLines);

        //act
        var orderPartsCancelledOptional = converter.convert(new EnrichedPickJobPickingFinished(pickJobPicking, facility));

        //assert
        assertThat(orderPartsCancelledOptional).as("Event is present").isPresent();

        orderPartsCancelledOptional.ifPresent(orderPartsCancelled ->
            assertThat(orderPartsCancelled.getOrderLines()).as("The order lines list is not empty").isNotEmpty());

        orderPartsCancelledOptional.ifPresent(orderPartsCancelled ->
            assertThat(orderPartsCancelled.getOrderLines()).as("There is only 1 order line in the OLC event").hasSize(1));

        orderPartsCancelledOptional.ifPresent(orderPartsCancelled ->
            assertThat(orderPartsCancelled.getOrderLines().get(0).getQuantity()).as("Quantity of order line is 1").isEqualTo(1));

        orderPartsCancelledOptional.ifPresent(orderPartsCancelled ->
            assertThat(orderPartsCancelled.getOrderId()).as("Order id is correct").isEqualTo(pickJobPicking.getTenantOrderId()));

        orderPartsCancelledOptional.ifPresent(orderPartsCancelled ->
            assertThat(orderPartsCancelled.getWarehouse()).as("Warehouse is the correct one for this order").isEqualTo(WAREHOUSE));
    }

    @Test
    void convert_givenPickingJobPickingFinished_returnsOrderPartsCancelledWithOneOrderLineQuantityOne() {

        var pickJobPicking = PickJobPickingFinishedPayloadGenerator.generate();

        final int quantity = 3;
        final int picked = 2;

        Facility facility = new Facility(String.valueOf(LOCATION_CHAIN_CODE), FacilityAddress.builder()
            .country(COUNTRY)
            .build());

        var pickedOrderLine = LineItem.builder()
            .article(PickJobPickingFinishedPayloadGenerator.generateArticle())
            .quantity(quantity)
            .picked(picked)
            .build();

        var orderLines = List.of(pickedOrderLine);
        pickJobPicking.setPickLineItems(orderLines);

        //act
        Optional<OrderPartsCancelled> orderPartsCancelledOptional = converter.convert(new EnrichedPickJobPickingFinished(pickJobPicking, facility));

        //assert
        assertThat(orderPartsCancelledOptional).as("Event is present").isPresent();

        orderPartsCancelledOptional.ifPresent(orderPartsCancelled ->
            assertThat(orderPartsCancelled.getOrderLines()).as("The order lines list is not empty").isNotEmpty());

        orderPartsCancelledOptional.ifPresent(orderPartsCancelled ->
            assertThat(orderPartsCancelled.getOrderLines()).as("There is only 1 order line in the OLC event").hasSize(1));

        orderPartsCancelledOptional.ifPresent(orderPartsCancelled ->
            assertThat(orderPartsCancelled.getOrderLines().get(0).getQuantity()).as("Quantity of order line is 1").isEqualTo(1));

        orderPartsCancelledOptional.ifPresent(orderPartsCancelled ->
            assertThat(orderPartsCancelled.getWarehouse()).as("Warehouse is the correct one for this order").isEqualTo(WAREHOUSE));
    }

    @Test
    void convert_givenPickingJobPickingFinished_returnsNoOrderPartsCancelled() {

        var pickJobPicking = PickJobPickingFinishedPayloadGenerator.generate();

        final int quantity = 3;
        final int picked = 3;

        Facility facility = new Facility(String.valueOf(LOCATION_CHAIN_CODE), FacilityAddress.builder()
            .country(COUNTRY)
            .build());

        var pickedOrderLine = LineItem.builder()
            .article(PickJobPickingFinishedPayloadGenerator.generateArticle())
            .quantity(quantity)
            .picked(picked)
            .build();

        var orderLines = List.of(pickedOrderLine);
        pickJobPicking.setPickLineItems(orderLines);

        //act
        var orderPartsCancelled = converter.convert(new EnrichedPickJobPickingFinished(pickJobPicking, facility));

        //assert
        assertThat(orderPartsCancelled).as("There is no OPC event").isEmpty();
    }

    @Test
    void given_validPickJobAborted_conversionHasBeenDelegated() {
        // arrange
        var source = new EnrichedPickJobAborted(null, null);

        // act & assert
        converter.convert(source);
        verify(orderPartsRejectedConverter).convert(source);
    }

    @Test
    void given_validOrderCancelled_conversionHasBeenDelegated() {
        // arrange
        var orderCancelled = OrderCancelledPayloadGenerator.generate().build();

        // act & assert
        converter.convert(orderCancelled);
        verify(orderPartsRejectedConverter).convert(orderCancelled);
    }
}
