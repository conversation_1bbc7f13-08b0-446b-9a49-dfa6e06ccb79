package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.PhoneNumber;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import util.OrderPartsCreatedGenerator;

import java.util.List;

import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.HOME;
import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.LOCKER;
import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.PICKUP;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class BelgiumAddressServiceTest {

    @InjectMocks
    private BelgiumAddressService belgiumAddressService;

    @Test
    void forLocker_givenAnOrder_correctAddressesAreReturned() {
        // arrange
        var order = OrderPartsCreatedGenerator.generate2DifferentAddresses("BE");
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(LOCKER)
            .build();

        // act
        var addresses = belgiumAddressService.generateAddresses(order, deliveryOptionQuery);

        // assert
        var address = ConsumerAddress.builder()
            .addressType(ConsumerAddress.AddressType.PARCEL_LOCKER)
            .additionalAddressInfo(OrderPartsCreatedGenerator.PICK_UP_POINT_CODE)
            .firstName(OrderPartsCreatedGenerator.FIRST_NAME)
            .lastName(OrderPartsCreatedGenerator.LAST_NAME)
            .street(OrderPartsCreatedGenerator.ADDRESS)
            .houseNumber(OrderPartsCreatedGenerator.HOUSE_NUMBER)
            .postalCode(OrderPartsCreatedGenerator.ZIPCODE)
            .city(OrderPartsCreatedGenerator.CITY)
            .country("BE")
            .phoneNumbers(List.of(PhoneNumber.builder()
                .type("MOBILE")
                .value(OrderPartsCreatedGenerator.PHONE_NUMBER)
                .build()))
            .build();
        var invoiceAddress = ConsumerAddress.builder()
            .firstName(OrderPartsCreatedGenerator.BILLING_FIRST_NAME)
            .lastName(OrderPartsCreatedGenerator.BILLING_LAST_NAME)
            .companyName(null)
            .addressType(ConsumerAddress.AddressType.INVOICE_ADDRESS)
            .city(OrderPartsCreatedGenerator.BILLING_CITY)
            .country("BE")
            .houseNumber(OrderPartsCreatedGenerator.BILLING_HOUSE_NUMER)
            .postalCode(OrderPartsCreatedGenerator.BILLING_ZIPCODE)
            .street(OrderPartsCreatedGenerator.BILLING_ADDRESS_1)
            .build();

        assertThat(addresses)
            .usingRecursiveFieldByFieldElementComparator()
            .containsExactlyInAnyOrder(address, invoiceAddress);
    }

    @Test
    void forPickup_givenAnOrder_correctAddressesAreReturned() {
        // arrange
        var order = OrderPartsCreatedGenerator.generate2DifferentAddresses("BE");
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(PICKUP)
            .build();

        // act
        var addresses = belgiumAddressService.generateAddresses(order, deliveryOptionQuery);

        // assert
        var address = ConsumerAddress.builder()
            .addressType(ConsumerAddress.AddressType.PARCEL_LOCKER)
            .additionalAddressInfo(OrderPartsCreatedGenerator.PICK_UP_POINT_CODE)
            .firstName(OrderPartsCreatedGenerator.FIRST_NAME)
            .lastName(OrderPartsCreatedGenerator.LAST_NAME)
            .street(OrderPartsCreatedGenerator.ADDRESS)
            .houseNumber(OrderPartsCreatedGenerator.HOUSE_NUMBER)
            .postalCode(OrderPartsCreatedGenerator.ZIPCODE)
            .city(OrderPartsCreatedGenerator.CITY)
            .country("BE")
            .phoneNumbers(List.of(PhoneNumber.builder()
                .type("MOBILE")
                .value(OrderPartsCreatedGenerator.PHONE_NUMBER)
                .build()))
            .build();
        var invoiceAddress = ConsumerAddress.builder()
            .firstName(OrderPartsCreatedGenerator.BILLING_FIRST_NAME)
            .lastName(OrderPartsCreatedGenerator.BILLING_LAST_NAME)
            .companyName(null)
            .addressType(ConsumerAddress.AddressType.INVOICE_ADDRESS)
            .city(OrderPartsCreatedGenerator.BILLING_CITY)
            .country("BE")
            .houseNumber(OrderPartsCreatedGenerator.BILLING_HOUSE_NUMER)
            .postalCode(OrderPartsCreatedGenerator.BILLING_ZIPCODE)
            .street(OrderPartsCreatedGenerator.BILLING_ADDRESS_1)
            .build();

        assertThat(addresses)
            .usingRecursiveFieldByFieldElementComparator()
            .containsExactlyInAnyOrder(address, invoiceAddress);
    }

    @Test
    void forHomeDelivery_givenAnOrder_correctAddressIsReturned() {
        // arrange
        var order = OrderPartsCreatedGenerator.generate("BE");
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(HOME)
            .build();

        // act
        var addresses = belgiumAddressService.generateAddresses(order, deliveryOptionQuery);

        // assert
        var address = ConsumerAddress.builder()
            .addressType(ConsumerAddress.AddressType.POSTAL_ADDRESS)
            .additionalAddressInfo(OrderPartsCreatedGenerator.PICK_UP_POINT_CODE)
            .firstName(OrderPartsCreatedGenerator.FIRST_NAME)
            .lastName(OrderPartsCreatedGenerator.LAST_NAME)
            .street(OrderPartsCreatedGenerator.ADDRESS)
            .houseNumber(OrderPartsCreatedGenerator.HOUSE_NUMBER)
            .postalCode(OrderPartsCreatedGenerator.ZIPCODE)
            .city(OrderPartsCreatedGenerator.CITY)
            .country("BE")
            .phoneNumbers(List.of(PhoneNumber.builder()
                .type("MOBILE")
                .value(OrderPartsCreatedGenerator.PHONE_NUMBER)
                .build()))
            .build();

        assertThat(addresses)
            .usingRecursiveFieldByFieldElementComparator()
            .containsExactly(address);
    }

    @Test
    void getCountry_mustReturnGermany() {
        // act & assert
        assertThat(belgiumAddressService.getCountry()).isEqualTo("BE");
    }

    @Test
    void getCarrier_mustReturnStar() {
        assertThat(belgiumAddressService.getCarrier()).isEqualTo("*");
    }

    @Test
    void getDeliveryType_mustReturnStar() {
        assertThat(belgiumAddressService.getDeliveryType()).isEqualTo("*");
    }

}
