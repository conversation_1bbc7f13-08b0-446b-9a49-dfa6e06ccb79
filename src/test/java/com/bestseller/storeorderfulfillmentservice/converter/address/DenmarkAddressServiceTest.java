package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.PhoneNumber;
import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import util.OrderPartsCreatedGenerator;

import java.util.List;
import java.util.stream.Stream;

import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.HOME;
import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.LOCKER;
import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.PICKUP;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@ExtendWith(MockitoExtension.class)
class DenmarkAddressServiceTest {

    private static final String GLS_CARRIER = "GLS";

    @InjectMocks
    private DenmarkAddressService denmarkAddressService;

    @Test
    void getCountry_mustReturnDenmark() {
        assertThat(denmarkAddressService.getCountry())
            .isEqualTo(CountryCode.DK.getAlpha2());
    }

    @Test
    void getCarrier_mustReturnStar() {
        assertThat(denmarkAddressService.getCarrier())
            .isEqualTo("*");
    }

    @Test
    void getDeliveryType_mustReturnStar() {
        assertThat(denmarkAddressService.getDeliveryType())
            .isEqualTo("*");
    }

    @Test
    void forLocker_givenAnyParameter_throwsException() {
        // arrange
        var orderPartsCreated = new OrderPartsCreated();
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(LOCKER)
            .build();

        // act & assert
        assertThatThrownBy(() -> denmarkAddressService.generateAddresses(orderPartsCreated, deliveryOptionQuery));
    }

    @Test
    void forHomeDelivery_givenAnOrder_correctAddressIsReturned() {
        // arrange
        var order = OrderPartsCreatedGenerator.generate("DK");
        order.getOrderDetails().setCarrier("POSTNORD");
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(HOME)
            .build();

        // act
        var addresses = denmarkAddressService.generateAddresses(order, deliveryOptionQuery);

        // assert
        var address = ConsumerAddress.builder()
            .firstName(OrderPartsCreatedGenerator.FIRST_NAME)
            .lastName(OrderPartsCreatedGenerator.LAST_NAME)
            .companyName(null)
            .addressType(ConsumerAddress.AddressType.POSTAL_ADDRESS)
            .city(OrderPartsCreatedGenerator.CITY)
            .country("DK")
            .houseNumber(OrderPartsCreatedGenerator.HOUSE_NUMBER)
            .postalCode(OrderPartsCreatedGenerator.ZIPCODE)
            .street(OrderPartsCreatedGenerator.ADDRESS)
            .additionalAddressInfo(OrderPartsCreatedGenerator.PICK_UP_POINT_CODE)
            .phoneNumbers(List.of(PhoneNumber.builder()
                .type("MOBILE")
                .value(OrderPartsCreatedGenerator.PHONE_NUMBER)
                .build()))
            .build();

        assertThat(addresses)
            .usingRecursiveFieldByFieldElementComparator()
            .containsExactly(address);
    }

    @Test
    void forPickup_givenAnOrder_correctAddressesAreReturned() {
        // arrange
        var order = OrderPartsCreatedGenerator.generate2DifferentAddresses("DK");
        order.getOrderDetails().setCarrier("POSTNORD");
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(PICKUP)
            .build();

        // act
        var addresses = denmarkAddressService.generateAddresses(order, deliveryOptionQuery);

        // assert
        var expected = List.of(
            ConsumerAddress.builder()
                .firstName(OrderPartsCreatedGenerator.FIRST_NAME)
                .lastName(OrderPartsCreatedGenerator.LAST_NAME)
                .addressType(ConsumerAddress.AddressType.PARCEL_LOCKER)
                .city(OrderPartsCreatedGenerator.CITY)
                .country("DK")
                .houseNumber(OrderPartsCreatedGenerator.HOUSE_NUMBER)
                .postalCode(OrderPartsCreatedGenerator.ZIPCODE)
                .street(OrderPartsCreatedGenerator.ADDRESS)
                .additionalAddressInfo(OrderPartsCreatedGenerator.PICK_UP_POINT_CODE)
                .phoneNumbers(List.of(PhoneNumber.builder()
                    .type("MOBILE")
                    .value(OrderPartsCreatedGenerator.PHONE_NUMBER)
                    .build()))
                .build(),
            ConsumerAddress.builder()
                .firstName(OrderPartsCreatedGenerator.FIRST_NAME)
                .lastName(OrderPartsCreatedGenerator.LAST_NAME)
                .addressType(ConsumerAddress.AddressType.INVOICE_ADDRESS)
                .city(OrderPartsCreatedGenerator.BILLING_CITY)
                .country("DK")
                .houseNumber(OrderPartsCreatedGenerator.BILLING_HOUSE_NUMER)
                .postalCode(OrderPartsCreatedGenerator.BILLING_ZIPCODE)
                .street(OrderPartsCreatedGenerator.BILLING_ADDRESS_1)
                .additionalAddressInfo(OrderPartsCreatedGenerator.PICK_UP_POINT_CODE)
                .phoneNumbers(List.of(PhoneNumber.builder()
                    .type("MOBILE")
                    .value(OrderPartsCreatedGenerator.PHONE_NUMBER)
                    .build()))
                .build()
        );

        assertThat(addresses)
            .usingRecursiveFieldByFieldElementComparator()
            .containsExactlyInAnyOrderElementsOf(expected);
    }

    @ParameterizedTest
    @MethodSource("glsDkParcelLockerIdData")
    void forPickup_givenShippingAddress3_correctAdditionalAddressInfoIsReturned(
        String carrier,
        String addressLine3,
        String expectedAdditionalAddressInfo
    ) {
        // arrange
        var order = OrderPartsCreatedGenerator.generate2DifferentAddresses("DK");
        order.getOrderDetails().setCarrier(carrier);
        order.getShippingInformation().getShippingAddress().setAddressLine3(addressLine3);
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(PICKUP)
            .build();

        // act
        var actualAdditionalAddressInfo = denmarkAddressService.generateAddresses(order, deliveryOptionQuery).stream()
            .filter(address -> address.getAddressType() == ConsumerAddress.AddressType.PARCEL_LOCKER)
            .findFirst()
            .map(ConsumerAddress::getAdditionalAddressInfo)
            .orElse(null);

        // assert
        assertThat(actualAdditionalAddressInfo).isEqualTo(expectedAdditionalAddressInfo);
    }

    private static Stream<Arguments> glsDkParcelLockerIdData() {
        return Stream.of(
            Arguments.of(GLS_CARRIER, null, null),
            Arguments.of(GLS_CARRIER, "", null),
            Arguments.of(GLS_CARRIER, "   ", null),
            Arguments.of(GLS_CARRIER, "95105", "2080095105"),
            Arguments.of(GLS_CARRIER, "97273", "2080097273"),
            Arguments.of(GLS_CARRIER, "4597273", "2084597273"),
            Arguments.of(GLS_CARRIER, "20805", "2080020805"),
            Arguments.of(GLS_CARRIER, "2084597273", "2084597273"),
            Arguments.of(GLS_CARRIER, "20814597273", "20814597273"),
            Arguments.of(null, "95105", "95105"),
            Arguments.of("POSTNORD", "97273", "97273")
        );
    }

}
