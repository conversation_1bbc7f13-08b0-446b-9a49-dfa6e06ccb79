package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.exception.AddressNotImplementToCountry;
import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.PhoneNumber;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import util.OrderPartsCreatedGenerator;

import java.util.List;

import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.HOME;
import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.LOCKER;
import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.PICKUP;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@ExtendWith(MockitoExtension.class)
class GermanyAddressServiceTest {

    @InjectMocks
    private GermanyAddressService germanyAddressService;

    @Test
    void forLocker_givenAnyParameter_throwsException() {
        // arrange
        var orderPartsCreated = new OrderPartsCreated();
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(LOCKER)
            .build();

        // act & assert
        assertThatThrownBy(() -> germanyAddressService.generateAddresses(orderPartsCreated, deliveryOptionQuery))
            .isInstanceOf(AddressNotImplementToCountry.class)
            .hasMessage("Address conversion method forLocker not implement to country DE");
    }

    @Test
    void forPickup_givenAnOrder_correctAddressesAreReturned() {
        // arrange
        var order = OrderPartsCreatedGenerator.generate("DE");
        order.getOrderDetails().setCarrier("DHL");
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(PICKUP)
            .build();

        // act
        var addresses = germanyAddressService.generateAddresses(order, deliveryOptionQuery);

        // assert
        ConsumerAddress address = ConsumerAddress.builder()
            .firstName(OrderPartsCreatedGenerator.FIRST_NAME)
            .lastName(OrderPartsCreatedGenerator.LAST_NAME)
            .companyName(null)
            .addressType(ConsumerAddress.AddressType.POSTAL_ADDRESS)
            .city(OrderPartsCreatedGenerator.CITY)
            .country("DE")
            .houseNumber(OrderPartsCreatedGenerator.HOUSE_NUMBER)
            .postalCode(OrderPartsCreatedGenerator.ZIPCODE)
            .street(OrderPartsCreatedGenerator.ADDRESS)
            .additionalAddressInfo(OrderPartsCreatedGenerator.PICK_UP_POINT_CODE)
            .phoneNumbers(List.of(PhoneNumber.builder()
                .type("MOBILE")
                .value(OrderPartsCreatedGenerator.PHONE_NUMBER)
                .build()))
            .build();

        assertThat(addresses)
            .hasSize(1);
        assertThat(addresses.get(0))
            .usingRecursiveComparison()
            .isEqualTo(address);
    }

    @Test
    void forHomeDelivery_givenAnOrder_correctAddressIsReturned() {
        // arrange
        var order = OrderPartsCreatedGenerator.generate("DE");
        order.getOrderDetails().setCarrier("DHL");
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(HOME)
            .build();

        // act
        var addresses = germanyAddressService.generateAddresses(order, deliveryOptionQuery);

        // assert
        ConsumerAddress address = ConsumerAddress.builder()
            .firstName(OrderPartsCreatedGenerator.FIRST_NAME)
            .lastName(OrderPartsCreatedGenerator.LAST_NAME)
            .companyName(null)
            .addressType(ConsumerAddress.AddressType.POSTAL_ADDRESS)
            .city(OrderPartsCreatedGenerator.CITY)
            .country("DE")
            .houseNumber(OrderPartsCreatedGenerator.HOUSE_NUMBER)
            .postalCode(OrderPartsCreatedGenerator.ZIPCODE)
            .street(OrderPartsCreatedGenerator.ADDRESS)
            .additionalAddressInfo(OrderPartsCreatedGenerator.PICK_UP_POINT_CODE)
            .phoneNumbers(List.of(PhoneNumber.builder()
                .type("MOBILE")
                .value(OrderPartsCreatedGenerator.PHONE_NUMBER)
                .build()))
            .build();

        assertThat(addresses)
            .hasSize(1);
        assertThat(addresses.get(0))
            .usingRecursiveComparison()
            .isEqualTo(address);

    }

    @Test
    void getCountry_mustReturnGermany() {
        // arrange
        // No arrange needed

        // act & assert
        assertThat(germanyAddressService.getCountry())
            .isEqualTo("DE");
    }

    @Test
    void getCarrier_mustReturnStar() {
        assertThat(germanyAddressService.getCarrier())
            .isEqualTo("*");
    }

    @Test
    void getDeliveryType_mustReturnStar() {
        assertThat(germanyAddressService.getDeliveryType())
            .isEqualTo("*");
    }

}
