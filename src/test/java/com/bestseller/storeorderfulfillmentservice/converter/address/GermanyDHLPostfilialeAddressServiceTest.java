package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.common.AdditionalInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.exception.AddressNotImplement;
import com.bestseller.storeorderfulfillmentservice.exception.InvalidMessageException;
import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.PhoneNumber;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import util.OrderPartsCreatedGenerator;

import java.util.HashSet;
import java.util.List;

import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.HOME;
import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.LOCKER;
import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.PICKUP;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@ExtendWith(MockitoExtension.class)
class GermanyDHLPostfilialeAddressServiceTest {

    private static final String DHL = "DHL";

    @InjectMocks
    private GermanyDHLPostfilialeAddressService germanyDHLPostfilialeAddressService;

    @Test
    void getCarrier_mustReturnDHL() {
        assertThat(germanyDHLPostfilialeAddressService.getCarrier())
            .isEqualTo(DHL);
    }

    @Test
    void getDeliveryType_mustReturnPickup() {
        assertThat(germanyDHLPostfilialeAddressService.getDeliveryType())
            .isEqualTo("PICKUP");
    }

    @Test
    void forHome_givenAnyParameter_throwsException() {
        // arrange
        var orderPartsCreated = new OrderPartsCreated();
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(HOME)
            .build();

        // act & assert
        assertThatThrownBy(() -> germanyDHLPostfilialeAddressService.generateAddresses(orderPartsCreated, deliveryOptionQuery))
            .isInstanceOf(AddressNotImplement.class)
            .hasMessage("Address conversion method Home delivery not implement to country DE for carrier DHL.");
    }

    @Test
    void forLocker_givenAnyParameter_throwsException() {
        // arrange
        var orderPartsCreated = new OrderPartsCreated();
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(LOCKER)
            .build();

        // act & assert
        assertThatThrownBy(() -> germanyDHLPostfilialeAddressService.generateAddresses(orderPartsCreated, deliveryOptionQuery))
            .isInstanceOf(AddressNotImplement.class)
            .hasMessage("Address conversion method Locker delivery not implement to country DE for carrier DHL.");
    }

    @Test
    void forPickup_givenAnOrder_correctAddressIsReturned() {
        // arrange
        var order = OrderPartsCreatedGenerator.generate("DE");
        order.getOrderDetails().setCarrier(DHL);
        order.getShippingInformation().setAdditionalInformation(new HashSet<>(
            List.of(
                new AdditionalInformation()
                    .withKey(OrderPartsCreatedGenerator.CARRIER_CUSTOMER_NUMBER_LITERAL)
                    .withValue(OrderPartsCreatedGenerator.CARRIER_CUSTOMER_NUMBER)
            )));
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(PICKUP)
            .build();

        // act
        var addresses = germanyDHLPostfilialeAddressService.generateAddresses(order, deliveryOptionQuery);

        // assert
        ConsumerAddress address = ConsumerAddress.builder()
            .firstName(OrderPartsCreatedGenerator.FIRST_NAME)
            .lastName(OrderPartsCreatedGenerator.LAST_NAME)
            .companyName(null)
            .addressType(ConsumerAddress.AddressType.POSTAL_ADDRESS)
            .city(OrderPartsCreatedGenerator.CITY)
            .country("DE")
            .houseNumber(OrderPartsCreatedGenerator.PICK_UP_POINT_CODE)
            .postalCode(OrderPartsCreatedGenerator.ZIPCODE)
            .street("Postfiliale")
            .additionalAddressInfo(OrderPartsCreatedGenerator.CARRIER_CUSTOMER_NUMBER)
            .phoneNumbers(List.of(PhoneNumber.builder()
                .type("MOBILE")
                .value(OrderPartsCreatedGenerator.PHONE_NUMBER)
                .build()))
            .build();

        assertThat(addresses)
            .hasSize(1);
        assertThat(addresses.get(0))
            .usingRecursiveComparison()
            .isEqualTo(address);

    }

    @Test
    void forPickup_givenAnOrderWithoutCustomerPostNumber_correctAddressIsReturned() {
        // arrange
        var order = OrderPartsCreatedGenerator.generate("DE");
        order.getOrderDetails().setCarrier(DHL);
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(PICKUP)
            .build();

        // act
        var addresses = germanyDHLPostfilialeAddressService.generateAddresses(order, deliveryOptionQuery);

        // assert
        ConsumerAddress address = ConsumerAddress.builder()
            .firstName(OrderPartsCreatedGenerator.FIRST_NAME)
            .lastName(OrderPartsCreatedGenerator.LAST_NAME)
            .companyName(null)
            .addressType(ConsumerAddress.AddressType.POSTAL_ADDRESS)
            .city(OrderPartsCreatedGenerator.CITY)
            .country("DE")
            .houseNumber(OrderPartsCreatedGenerator.PICK_UP_POINT_CODE)
            .postalCode(OrderPartsCreatedGenerator.ZIPCODE)
            .street("Postfiliale")
            .phoneNumbers(List.of(PhoneNumber.builder()
                .type("MOBILE")
                .value(OrderPartsCreatedGenerator.PHONE_NUMBER)
                .build()))
            .build();

        assertThat(addresses)
            .hasSize(1);
        assertThat(addresses.get(0))
            .usingRecursiveComparison()
            .isEqualTo(address);

    }

    @Test
    void forPickup_givenAnInvalidOrder_throwsException() {
        // arrange
        var order = OrderPartsCreatedGenerator.generate("DE");
        order.getOrderDetails().setCarrier(DHL);
        order.getShippingInformation().setAdditionalInformation(null);
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(PICKUP)
            .build();

        assertThatThrownBy(() -> germanyDHLPostfilialeAddressService.generateAddresses(order, deliveryOptionQuery))
            .isInstanceOf(InvalidMessageException.class)
            .hasMessage("Additional information is missing in the OrderPartsCreated message.");
    }

    @Test
    void forPickup_givenAnOrderWithoutPostfilialeNumber_throwsException() {
        // arrange
        var order = OrderPartsCreatedGenerator.generate("DE");
        order.getOrderDetails().setCarrier(DHL);
        order.getShippingInformation().getShippingAddress().setAddressLine3("");
        order.getShippingInformation().setAdditionalInformation(new HashSet<>(
            List.of(
                new AdditionalInformation()
                    .withKey(OrderPartsCreatedGenerator.CARRIER_CUSTOMER_NUMBER_LITERAL)
                    .withValue(OrderPartsCreatedGenerator.CARRIER_CUSTOMER_NUMBER)
            )));
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(PICKUP)
            .build();

        assertThatThrownBy(() -> germanyDHLPostfilialeAddressService.generateAddresses(order, deliveryOptionQuery))
            .isInstanceOf(InvalidMessageException.class)
            .hasMessage("AddressLine3 is missing on the shipping address.");
    }

}
