package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.exception.AddressNotImplementToCountry;
import com.bestseller.storeorderfulfillmentservice.model.DeliveryType;
import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.PhoneNumber;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import util.OrderPartsCreatedGenerator;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
class NetherlandsAddressServiceTest {

    @InjectMocks
    private NetherlandsAddressService netherlandsAddressService;

    @Test
    void forLocker_givenAnyParameter_throwsException() {
        // arrange
        var orderPartsCreated = new OrderPartsCreated();
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(DeliveryType.LOCKER)
            .build();

        // act & assert
        assertThrows(
            AddressNotImplementToCountry.class,
            () -> netherlandsAddressService.generateAddresses(orderPartsCreated, deliveryOptionQuery)
        );
    }

    @Test
    void forPickup_givenAnyParameter_throwsException() {
        // arrange
        var orderPartsCreated = new OrderPartsCreated();
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(DeliveryType.PICKUP)
            .build();

        // act & assert
        assertThrows(
            AddressNotImplementToCountry.class,
            () -> netherlandsAddressService.generateAddresses(orderPartsCreated, deliveryOptionQuery)
        );
    }

    @Test
    void forHomeDelivery_givenAnOrder_correctAddressIsReturned() {
        // arrange
        var order = OrderPartsCreatedGenerator.generate("NL");
        order.getOrderDetails().setCarrier("DHL");
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(DeliveryType.HOME)
            .build();

        // act
        var addresses = netherlandsAddressService.generateAddresses(order, deliveryOptionQuery);

        // assert
        var address = ConsumerAddress.builder()
            .firstName(OrderPartsCreatedGenerator.FIRST_NAME)
            .lastName(OrderPartsCreatedGenerator.LAST_NAME)
            .companyName(null)
            .addressType(ConsumerAddress.AddressType.POSTAL_ADDRESS)
            .city(OrderPartsCreatedGenerator.CITY)
            .country("NL")
            .houseNumber(OrderPartsCreatedGenerator.HOUSE_NUMBER)
            .postalCode(OrderPartsCreatedGenerator.ZIPCODE)
            .street(OrderPartsCreatedGenerator.ADDRESS)
            .additionalAddressInfo(OrderPartsCreatedGenerator.PICK_UP_POINT_CODE)
            .phoneNumbers(List.of(PhoneNumber.builder()
                .type("MOBILE")
                .value(OrderPartsCreatedGenerator.PHONE_NUMBER)
                .build()))
            .build();

        assertThat(addresses)
            .usingRecursiveFieldByFieldElementComparator()
            .containsExactly(address);
    }

    @Test
    void getCountry_mustReturnNetherlands() {
        assertThat(netherlandsAddressService.getCountry()).isEqualTo("NL");
    }

    @Test
    void getCarrier_mustReturnStar() {
        assertThat(netherlandsAddressService.getCarrier()).isEqualTo("*");
    }

    @Test
    void getDeliveryType_mustReturnStar() {
        assertThat(netherlandsAddressService.getDeliveryType()).isEqualTo("*");
    }

}
