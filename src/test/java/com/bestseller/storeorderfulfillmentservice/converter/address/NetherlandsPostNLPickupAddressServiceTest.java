package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.exception.AddressNotImplementToCountry;
import com.bestseller.storeorderfulfillmentservice.model.DeliveryType;
import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.PhoneNumber;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import util.OrderPartsCreatedGenerator;

import java.util.List;

import static com.bestseller.storeorderfulfillmentservice.model.DeliveryType.PICKUP;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
class NetherlandsPostNLPickupAddressServiceTest {

    @InjectMocks
    private NetherlandsPostNLPickupAddressService netherlandsPostNLPickupAddressService;

    @Test
    void forLocker_givenAnyParameter_throwsException() {
        // arrange
        var orderPartsCreated = new OrderPartsCreated();
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(DeliveryType.LOCKER)
            .build();

        // act & assert
        assertThrows(
            AddressNotImplementToCountry.class,
            () -> netherlandsPostNLPickupAddressService.generateAddresses(orderPartsCreated, deliveryOptionQuery)
        );
    }

    @Test
    void forPickup_givenAnOrder_correctAddressesAreReturned() {
        // arrange
        var order = OrderPartsCreatedGenerator.generate2DifferentAddresses("NL");
        order.getOrderDetails().setCarrier("POSTNL");
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(PICKUP)
            .build();

        // act
        var addresses = netherlandsPostNLPickupAddressService.generateAddresses(order, deliveryOptionQuery);

        // assert
        var invoiceAddress = ConsumerAddress.builder()
            .firstName(OrderPartsCreatedGenerator.BILLING_FIRST_NAME)
            .lastName(OrderPartsCreatedGenerator.BILLING_LAST_NAME)
            .companyName(null)
            .addressType(ConsumerAddress.AddressType.INVOICE_ADDRESS)
            .city(OrderPartsCreatedGenerator.BILLING_CITY)
            .country("NL")
            .houseNumber(OrderPartsCreatedGenerator.BILLING_HOUSE_NUMER)
            .postalCode(OrderPartsCreatedGenerator.BILLING_ZIPCODE)
            .street(OrderPartsCreatedGenerator.BILLING_ADDRESS_1)
            .build();

        var lockerAddress = ConsumerAddress.builder()
            .firstName(OrderPartsCreatedGenerator.FIRST_NAME)
            .lastName(OrderPartsCreatedGenerator.LAST_NAME)
            .addressType(ConsumerAddress.AddressType.PARCEL_LOCKER)
            .city(OrderPartsCreatedGenerator.BILLING_CITY)
            .country("NL")
            .houseNumber(OrderPartsCreatedGenerator.BILLING_HOUSE_NUMER)
            .postalCode(OrderPartsCreatedGenerator.BILLING_ZIPCODE)
            .street(OrderPartsCreatedGenerator.BILLING_ADDRESS_1)
            .additionalAddressInfo(OrderPartsCreatedGenerator.PICK_UP_POINT_CODE)
            .companyName("POSTNL")
            .phoneNumbers(List.of(PhoneNumber.builder()
                .type("MOBILE")
                .value(OrderPartsCreatedGenerator.PHONE_NUMBER)
                .build()))
            .build();

        assertThat(addresses)
            .usingRecursiveFieldByFieldElementComparator()
            .containsExactlyInAnyOrder(invoiceAddress, lockerAddress);
    }

    @Test
    void forHomeDelivery_givenAnyParameter_throwsException() {
        // arrange
        var orderPartsCreated = new OrderPartsCreated();
        var deliveryOptionQuery = DeliveryOptionQuery.builder()
            .deliveryType(DeliveryType.HOME)
            .build();

        // act & assert
        assertThrows(
            AddressNotImplementToCountry.class,
            () -> netherlandsPostNLPickupAddressService.generateAddresses(orderPartsCreated, deliveryOptionQuery)
        );
    }

    @Test
    void getCountry_mustReturnNetherlands() {
        assertThat(netherlandsPostNLPickupAddressService.getCountry()).isEqualTo("NL");
    }

    @Test
    void getCarrier_mustReturnPostNL() {
        assertThat(netherlandsPostNLPickupAddressService.getCarrier()).isEqualTo("POSTNL");
    }

    @Test
    void getDeliveryType_mustReturnPickup() {
        assertThat(netherlandsPostNLPickupAddressService.getDeliveryType()).isEqualTo(DeliveryType.PICKUP.name());
    }
}
