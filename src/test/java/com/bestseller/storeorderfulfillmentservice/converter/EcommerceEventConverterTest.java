package com.bestseller.storeorderfulfillmentservice.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.configuration.Carrier;
import com.bestseller.storeorderfulfillmentservice.configuration.CarrierMappingProperties;
import com.bestseller.storeorderfulfillmentservice.converter.address.GermanyAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.GermanyDHLPackstationAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.NetherlandsAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.NetherlandsDHLPickupAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.NetherlandsPostNLPickupAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.NorwayAddressService;
import com.bestseller.storeorderfulfillmentservice.exception.AttributeNotFoundException;
import com.bestseller.storeorderfulfillmentservice.exception.CarrierNotSupported;
import com.bestseller.storeorderfulfillmentservice.model.DeliveryType;
import com.bestseller.storeorderfulfillmentservice.model.ServiceType;
import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.DeliveryPreferences;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.ServiceOption;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.Shipping;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.ecom.EnrichedOrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.FulfillmentOrderLine;
import com.bestseller.storeorderfulfillmentservice.model.pcs.ProductInformation;
import com.bestseller.storeorderfulfillmentservice.service.AddressConverterFinderService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import util.OrderPartsCreatedGenerator;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.Category.DESCRIPTIVE;
import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.Category.MISCELLANEOUS;
import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress.AddressType.INVOICE_ADDRESS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static util.OrderPartsCreatedGenerator.BRAND_JJ;
import static util.OrderPartsCreatedGenerator.EAN_2;
import static util.OrderPartsCreatedGenerator.EMAIL;
import static util.OrderPartsCreatedGenerator.ORDER_ID;
import static util.OrderPartsCreatedGenerator.PLACED_DATE;
import static util.OrderPartsCreatedGenerator.SHIPPING_FEES;

@ExtendWith(MockitoExtension.class)
@SuppressWarnings("VisibilityModifier")
class EcommerceEventConverterTest {

    private static final Carrier CARRIER = Carrier.builder()
        .key("CARRIER")
        .build();
    private static final String EXPECTED_BRAND_JJ = "jj";
    private static final String EXPECTED_BRAND_VM = "vm";
    private static final String EXPECTED_BRAND_NAME_JJ = "Jack & Jones";
    private static final String EXPECTED_BRAND_NAME_VM = "Vero Moda";
    private final MeterRegistry meterRegistry = new SimpleMeterRegistry();
    private final AddressConverterFinderService addressConverterFinderService = new AddressConverterFinderService();
    private EcommerceEventConverter converter;

    @Mock
    private OrderPartsRejectedConverter orderPartsRejectedConverter;
    @Mock
    private CarrierMappingProperties carrierMappingProperties;
    private final ZoneId timeZone = ZoneId.of("UTC");

    @BeforeEach
    void setUp() {
        addressConverterFinderService.put("NL_*_*", new NetherlandsAddressService());
        addressConverterFinderService.put("NL_DHL_PICKUP", new NetherlandsDHLPickupAddressService());
        addressConverterFinderService.put("NL_POSTNL_PICKUP", new NetherlandsPostNLPickupAddressService());
        addressConverterFinderService.put("DE_*_*", new GermanyAddressService());
        addressConverterFinderService.put("NO_*_*", new NorwayAddressService());
        addressConverterFinderService.put("DE_DHL_LOCKER", new GermanyDHLPackstationAddressService());

        converter = new EcommerceEventConverter(
            Clock.fixed(Instant.EPOCH, timeZone),
            addressConverterFinderService,
            meterRegistry,
            carrierMappingProperties,
            orderPartsRejectedConverter
        );
    }

    @Test
    void convert_givenValidInput_convertedObjectIsValid() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();

        // act
        var orderLinesExported = converter.convert(orderPartsCreated);

        // assert
        assertThat(orderLinesExported).as("Return has 2 items").hasSize(2);
        assertThat(orderLinesExported.get(0).getEan())
            .as("First item exported EAN matches")
            .isEqualTo(OrderPartsCreatedGenerator.EAN_1);
        assertThat(orderLinesExported.get(0).getQuantity())
            .as("First item exported quantity matches")
            .isEqualTo(OrderPartsCreatedGenerator.QUANTITY_1);
        assertThat(orderLinesExported.get(0).getWarehouse())
            .as("First item exported warehouse matches")
            .isEqualTo(OrderPartsCreatedGenerator.FULFILLMENT_NODE);
        assertThat(orderLinesExported.get(0).getExportDate())
            .as("First item exported date matches")
            .isEqualTo(ZonedDateTime.parse("1970-01-01T00:00Z[UTC]"));
        assertThat(orderLinesExported.get(1).getEan())
            .as("First item exported EAN matches")
            .isEqualTo(EAN_2);
        assertThat(orderLinesExported.get(1).getQuantity())
            .as("First item exported quantity matches")
            .isEqualTo(OrderPartsCreatedGenerator.QUANTITY_2);
        assertThat(orderLinesExported.get(1).getWarehouse())
            .as("First item exported warehouse matches")
            .isEqualTo(OrderPartsCreatedGenerator.FULFILLMENT_NODE);
        assertThat(orderLinesExported.get(1).getExportDate())
            .as("First item exported date matches")
            .isEqualTo(ZonedDateTime.parse("1970-01-01T00:00Z[UTC]"));
    }

    @Test
    void convertToOrderCreation_givenValidInput_convertedObjectIsValid() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        var enrichedOrderPartsCreated = EnrichedOrderPartsCreated.builder()
            .orderPartsCreated(orderPartsCreated)
            .productsInformation(OrderPartsCreatedGenerator.generateProductInformation())
            .build();
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        var orderCreation = converter.convertToOrderCreation(enrichedOrderPartsCreated);

        // assert
        assertThat(orderCreation.getOrderDate())
            .as("Order date matches")
            .isEqualTo(Instant.parse(PLACED_DATE));

        assertThat(orderCreation.getTenantOrderId())
            .as("Order number matches")
            .isEqualTo(ORDER_ID);

        assertThat(orderCreation.getConsumer().getEmail())
            .as("Email matches")
            .isEqualTo(EMAIL);
        assertThat(orderCreation.getCustomAttributes().shipmentCost())
            .as("Shipment cost matches")
            .isEqualTo(SHIPPING_FEES);

        assertThat(orderCreation.getCustomAttributes().brand())
            .as("Brand matches")
            .isEqualTo(BRAND_JJ);

        assertConsumerAddress(orderCreation.getConsumer().getAddresses(), 1);
        assertOrderLines(orderCreation.getOrderLineItems());
    }

    @Test
    void convertToOrderCreation_givenValidInputWithoutHouseNumber_convertedObjectIsValid() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        orderPartsCreated.getShippingInformation().getShippingAddress().setHouseNumber(null);
        EnrichedOrderPartsCreated enrichedOrderPartsCreated = withProductInfo(orderPartsCreated);
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        var orderCreation = converter.convertToOrderCreation(enrichedOrderPartsCreated);

        // assert
        assertThat(orderCreation.getConsumer().getAddresses().get(0).getHouseNumber())
            .as("House number is empty string")
            .isEqualTo(" ");
    }

    @Test
    void convertToOrderCreation_givenValidInputWithEmptyHouseNumber_convertedObjectIsValid() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        orderPartsCreated.getShippingInformation().getShippingAddress().setHouseNumber("");
        EnrichedOrderPartsCreated enrichedOrderPartsCreated = withProductInfo(orderPartsCreated);
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        var orderCreation = converter.convertToOrderCreation(enrichedOrderPartsCreated);

        // assert
        assertThat(orderCreation.getConsumer().getAddresses().get(0).getHouseNumber())
            .as("House number is empty string")
            .isEqualTo(" ");
    }

    @Test
    void convertToOrderCreation_givenPickupDutchOlympusOrder_addressLine3IsAdded() {
        // arrange
        final String pickupPointCode = "123456";
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        orderPartsCreated.getShippingInformation().getShippingAddress().setAddressLine3(pickupPointCode);
        orderPartsCreated.setFulfillmentNode("SHIP_FROM_STORE_NL");
        orderPartsCreated.setOrderId("OL" + orderPartsCreated.getOrderId());

        EnrichedOrderPartsCreated enrichedOrderPartsCreated = withProductInfo(orderPartsCreated);
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        var orderCreation = converter.convertToOrderCreation(enrichedOrderPartsCreated);

        // assert
        assertThat(orderCreation.getConsumer().getAddresses())
            .as("Dutch pickup addresses")
            .singleElement()
            .satisfies(address -> assertThat(address.getAdditionalAddressInfo())
                .as("additional address info")
                .contains(pickupPointCode));
    }

    @Test
    void convertToOrderCreation_givenNonPickupOrder_addressLine3IsNotAdded() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        orderPartsCreated.getShippingInformation().getShippingAddress().setAddressLine3(null);
        EnrichedOrderPartsCreated enrichedOrderPartsCreated = withProductInfo(orderPartsCreated);
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        var orderCreation = converter.convertToOrderCreation(enrichedOrderPartsCreated);

        // assert
        assertThat(orderCreation.getConsumer().getAddresses())
            .as("non-pickup addresses")
            .singleElement()
            .satisfies(address -> assertThat(address.getAdditionalAddressInfo())
                .as("additional address info")
                .isNull());
    }

    @Test
    void convertToOrderCreation_carrierNotFound_exceptionIsThrown() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        orderPartsCreated.getShippingInformation().getShippingAddress().setAddressLine3(null);
        EnrichedOrderPartsCreated enrichedOrderPartsCreated = withProductInfo(orderPartsCreated);
        when(carrierMappingProperties.getCarrier(any()))
            .thenThrow(new CarrierNotSupported(
                new DeliveryOptionQuery("NL", "CARRIER", DeliveryType.HOME, ServiceType.EXPRESS)));

        // act & assert
        assertThatThrownBy(() -> converter.convertToOrderCreation(enrichedOrderPartsCreated))
            .isInstanceOf(CarrierNotSupported.class)
            .hasMessage("Carrier not found for given country: NL, carrier: CARRIER, deliveryOption: HOME and serviceType: EXPRESS");
    }

    // Complementary scenery.
    @Test
    void convertToOrderCreation_givenValidInputWithoutPhoneNumber_convertedObjectIsValid() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        orderPartsCreated.getShippingInformation().getShippingAddress().setPhoneNumber(null);
        EnrichedOrderPartsCreated enrichedOrderPartsCreated = withProductInfo(orderPartsCreated);
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        var orderCreation = converter.convertToOrderCreation(enrichedOrderPartsCreated);

        // assert
        assertThat(orderCreation.getConsumer().getAddresses().get(0).getPhoneNumbers())
            .as("Phone number matches")
            .hasSize(0);
    }

    // Complementary scenery when mandatory attributes are not found.
    @Test
    void convertToOrderCreation_givenMandatoryAttributeNotFound_error() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        var enrichedOrderPartsCreated = EnrichedOrderPartsCreated.builder()
            .orderPartsCreated(orderPartsCreated)
            .productsInformation(Map.of(OrderPartsCreatedGenerator.EAN_1, ProductInformation.builder().build()))
            .build();
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act && assert
        var attributeNotFoundException = assertThrows(
            AttributeNotFoundException.class,
            () -> converter.convertToOrderCreation(enrichedOrderPartsCreated));

        assertThat(attributeNotFoundException).as("Exception")
            .hasMessageContaining("Attribute STYLE_OPTION is required but not found");

    }

    // Complementary scenery.
    @Test
    void convertToOrderCreation_noImageWasFound_convertedObjectIsValid() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        EnrichedOrderPartsCreated enrichedOrderPartsCreated = withProductInfo(orderPartsCreated);
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        var orderCreation = converter.convertToOrderCreation(enrichedOrderPartsCreated);

        // assert
        assertThat(orderCreation.getOrderLineItems())
            .as("order line items")
            .allSatisfy(orderLineItem -> assertThat(orderLineItem.getArticle().getImageUrl())
                .as("no image %s", orderLineItem)
                .isNull());
    }

    @Test
    void convertToOrderCreation_givenCarrierWithServices_convertedObjectIsValid() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        EnrichedOrderPartsCreated enrichedOrderPartsCreated = withProductInfo(orderPartsCreated);
        var carrier = "BPOST";
        when(carrierMappingProperties.getCarrier(any())).thenReturn(
            Carrier.builder()
                .key(carrier)
                .services(List.of("SIGNATURE"))
                .build()
        );

        // act
        var orderCreation = converter.convertToOrderCreation(enrichedOrderPartsCreated);

        // assert
        assertThat(orderCreation.getDeliveryPreferences())
            .usingRecursiveComparison()
            .isEqualTo(
                DeliveryPreferences.builder()
                    .shipping(
                        Shipping.builder()
                            .preferredCarriers(null)
                            .serviceLevel("DELIVERY")
                            .preferredCarriersWithProduct(
                                List.of(
                                    ServiceOption.builder()
                                        .carrierKey(carrier)
                                        .carrierServices(List.of("SIGNATURE"))
                                        .build()
                                )
                            )
                            .build()
                    )
                    .build()
            );
    }

    @ParameterizedTest
    @NullAndEmptySource
    void convertToOrderCreation_givenCarrierWithoutServices_convertedObjectIsValid(List<String> services) {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        EnrichedOrderPartsCreated enrichedOrderPartsCreated = withProductInfo(orderPartsCreated);
        var carrier = "BPOST";
        when(carrierMappingProperties.getCarrier(any())).thenReturn(
            Carrier.builder()
                .key(carrier)
                .services(services)
                .build()
        );

        // act
        var orderCreation = converter.convertToOrderCreation(enrichedOrderPartsCreated);

        // assert
        assertThat(orderCreation.getDeliveryPreferences())
            .usingRecursiveComparison()
            .isEqualTo(
                DeliveryPreferences.builder()
                    .shipping(
                        Shipping.builder()
                            .preferredCarriers(List.of(carrier))
                            .serviceLevel("DELIVERY")
                            .preferredCarriersWithProduct(null)
                            .build()
                    )
                    .build()
            );
    }

    private static EnrichedOrderPartsCreated withProductInfo(OrderPartsCreated orderPartsCreated) {
        return EnrichedOrderPartsCreated.builder()
            .orderPartsCreated(orderPartsCreated)
            .productsInformation(OrderPartsCreatedGenerator.generateProductInformationWithoutImage())
            .build();
    }

    private void assertOrderLines(List<FulfillmentOrderLine> orderLineItems) {
        assertThat(orderLineItems)
            .as("2 order lines")
            .hasSize(2);

        assertThat(orderLineItems)
            .as("order line items")
            .element(0)
            .satisfies(orderLine -> {
                assertThat(orderLine.getArticle().getEan())
                    .as("EAN in order line 1 matches")
                    .isEqualTo(OrderPartsCreatedGenerator.EAN_1);
                assertThat(orderLine.getArticle().getTitle())
                    .as("Title in order line 1 matches")
                    .isEqualTo(OrderPartsCreatedGenerator.PRODUCT_NAME_1);
                assertThat(orderLine.getArticle().getAttributes())
                    .as("Attributes in order line 1 matches")
                    .extracting("category", "key", "value")
                    .containsExactlyInAnyOrder(
                        Tuple.tuple(DESCRIPTIVE, "%%subtitle%%", "12218990"),
                        Tuple.tuple(DESCRIPTIVE, "COLOR", "Blue"),
                        Tuple.tuple(DESCRIPTIVE, "SIZE~LENGTH", "S/M ~ 10"),
                        Tuple.tuple(DESCRIPTIVE, "EAN", OrderPartsCreatedGenerator.EAN_1),
                        Tuple.tuple(DESCRIPTIVE, "PRICE", "11.01"),
                        Tuple.tuple(MISCELLANEOUS, "STYLE_OPTION", "12218990_BLUE"),
                        Tuple.tuple(MISCELLANEOUS, "EDI_STYLE_NAME", "Edi Style Name Ean 1"),
                        Tuple.tuple(MISCELLANEOUS, "BRAND", EXPECTED_BRAND_NAME_JJ)
                    );

                assertThat(orderLine.getQuantity())
                    .as("Quantity in order line 1 matches")
                    .isEqualTo(OrderPartsCreatedGenerator.QUANTITY_1);
                assertThat(orderLine.getShopPrice())
                    .as("Price in order line 1 matches")
                    .isEqualTo(OrderPartsCreatedGenerator.RETAIL_PRICE_1);
                assertThat(orderLine.getScannableCodes())
                    .as("Scannable barcodes in order line 1 matches")
                    .hasSize(1)
                    .contains(OrderPartsCreatedGenerator.EAN_1);
                assertThat(orderLine.getArticle().getImageUrl())
                    .as("Image in order line 1 matches")
                    .isEqualTo(OrderPartsCreatedGenerator.IMAGE_EAN_1);
            });

        assertThat(orderLineItems)
            .as("order line items")
            .element(1)
            .satisfies(orderLine -> {
                assertThat(orderLine.getArticle().getEan())
                    .as("EAN in order line 2 matches")
                    .isEqualTo(EAN_2);
                assertThat(orderLine.getArticle().getTitle())
                    .as("Title in order line 2 matches")
                    .isEqualTo(OrderPartsCreatedGenerator.PRODUCT_NAME_2);
                assertThat(orderLine.getArticle().getAttributes())
                    .as("Attributes in order line 2 matches")
                    .extracting("category", "key", "value")
                    .containsExactlyInAnyOrder(
                        Tuple.tuple(DESCRIPTIVE, "%%subtitle%%", "12218991"),
                        Tuple.tuple(DESCRIPTIVE, "COLOR", "Pink"),
                        Tuple.tuple(DESCRIPTIVE, "SIZE", "13"),
                        Tuple.tuple(DESCRIPTIVE, "EAN", EAN_2),
                        Tuple.tuple(DESCRIPTIVE, "PRICE", "19.0"),
                        Tuple.tuple(MISCELLANEOUS, "STYLE_OPTION", "12218991_PINK"),
                        Tuple.tuple(MISCELLANEOUS, "EDI_STYLE_NAME", "Edi Style Name Ean 2"),
                        Tuple.tuple(MISCELLANEOUS, "BRAND", EXPECTED_BRAND_NAME_VM)
                    );

                assertThat(orderLine.getQuantity())
                    .as("Quantity in order line 2 matches")
                    .isEqualTo(OrderPartsCreatedGenerator.QUANTITY_2);
                assertThat(orderLine.getShopPrice())
                    .as("Price in order line 2 matches")
                    .isEqualTo(OrderPartsCreatedGenerator.RETAIL_PRICE_2);
                assertThat(orderLine.getScannableCodes())
                    .as("Scannable barcodes in order line 2 matches")
                    .hasSize(1)
                    .contains(EAN_2);
                assertThat(orderLine.getArticle().getImageUrl())
                    .as("Image in order line 2 matches")
                    .isEqualTo(OrderPartsCreatedGenerator.IMAGE_EAN_2);
            });
    }

    private void assertConsumerAddress(List<ConsumerAddress> consumerAddresses, int expectedSize) {
        assertThat(consumerAddresses)
            .as("Only one consumer address")
            .hasSize(expectedSize);
        assertThat(consumerAddresses)
            .as("consumer address")
            .allSatisfy(consumerAddress -> {
                assertThat(consumerAddress.getFirstName())
                    .as("First names matches")
                    .isEqualTo(OrderPartsCreatedGenerator.FIRST_NAME);
                assertThat(consumerAddress.getLastName())
                    .as("Last names matches")
                    .isEqualTo(OrderPartsCreatedGenerator.LAST_NAME);
                assertThat(consumerAddress.getStreet())
                    .as("Street matches")
                    .isEqualTo(OrderPartsCreatedGenerator.ADDRESS);
                assertThat(consumerAddress.getHouseNumber())
                    .as("House number matches")
                    .isEqualTo(OrderPartsCreatedGenerator.HOUSE_NUMBER);
                assertThat(consumerAddress.getPostalCode())
                    .as("Postal code matches")
                    .isEqualTo(OrderPartsCreatedGenerator.ZIPCODE);
                assertThat(consumerAddress.getCity())
                    .as("City matches")
                    .isEqualTo(OrderPartsCreatedGenerator.CITY);
                assertThat(consumerAddress.getCountry())
                    .as("Country matches")
                    .isEqualTo(OrderPartsCreatedGenerator.COUNTRY);
            })
            .filteredOn(consumerAddress -> consumerAddress.getAddressType() != INVOICE_ADDRESS)
            .singleElement().satisfies(consumerAddress -> {
                assertThat(consumerAddress.getPhoneNumbers())
                    .as("Object has only one phone number")
                    .hasSize(1);
                assertThat(consumerAddress.getPhoneNumbers())
                    .as("Phone number matches")
                    .hasSize(1)
                    .extracting("type", "value")
                    .contains(Tuple.tuple("MOBILE", OrderPartsCreatedGenerator.PHONE_NUMBER));
            });
    }

    @Test
    void convertToOrderCreation_givenValidInputWithBrands_convertedObjectIsValid() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        var enrichedOrderPartsCreated = EnrichedOrderPartsCreated.builder()
            .orderPartsCreated(orderPartsCreated)
            .productsInformation(OrderPartsCreatedGenerator.generateProductInformation())
            .build();
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        var orderCreation = converter.convertToOrderCreation(enrichedOrderPartsCreated);

        // assert
        assertThat(orderCreation.getOrderDate())
            .as("Order date matches")
            .isEqualTo(Instant.parse(PLACED_DATE));

        assertThat(orderCreation.getTenantOrderId())
            .as("Order number matches")
            .isEqualTo(ORDER_ID);

        assertThat(orderCreation.getOrderLineItems().get(0).getTags().get(0).getValue())
            .as("First order line contains correct brand abbreviation")
            .isEqualTo(EXPECTED_BRAND_JJ);

        assertThat(orderCreation.getOrderLineItems().get(1).getTags().get(0).getValue())
            .as("Second order line contains correct brand abbreviation")
            .isEqualTo(EXPECTED_BRAND_VM);
    }

    @Test
    void rejectOrder_givenEnrichedOrderPartsCreated_methodIsCalled() {
        // arrange
        var enrichedOrderParts = EnrichedOrderPartsCreated.builder()
            .orderPartsCreated(new OrderPartsCreated())
            .build();

        // act
        converter.rejectOrder(enrichedOrderParts);

        // assert
        verify(orderPartsRejectedConverter).convert(enrichedOrderParts.getOrderPartsCreated());
    }
}
