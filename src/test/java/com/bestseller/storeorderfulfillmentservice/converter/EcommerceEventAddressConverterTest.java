package com.bestseller.storeorderfulfillmentservice.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderDetails;
import com.bestseller.storeorderfulfillmentservice.configuration.Carrier;
import com.bestseller.storeorderfulfillmentservice.configuration.CarrierMappingProperties;
import com.bestseller.storeorderfulfillmentservice.converter.address.GermanyAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.GermanyDHLPackstationAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.NetherlandsAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.NetherlandsDHLPickupAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.NetherlandsPostNLPickupAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.NorwayAddressService;
import com.bestseller.storeorderfulfillmentservice.exception.AddressNotImplementToCountry;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.ecom.EnrichedOrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import com.bestseller.storeorderfulfillmentservice.service.AddressConverterFinderService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import util.OrderPartsCreatedGenerator;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;

import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress.AddressType.INVOICE_ADDRESS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static util.OrderPartsCreatedGenerator.ADDRESS;
import static util.OrderPartsCreatedGenerator.CITY;
import static util.OrderPartsCreatedGenerator.COMPANY_NAME;
import static util.OrderPartsCreatedGenerator.COUNTRY;
import static util.OrderPartsCreatedGenerator.FIRST_NAME;
import static util.OrderPartsCreatedGenerator.HOUSE_NUMBER;
import static util.OrderPartsCreatedGenerator.LAST_NAME;
import static util.OrderPartsCreatedGenerator.SHIPPING_FEES;
import static util.OrderPartsCreatedGenerator.ZIPCODE;

@ExtendWith(MockitoExtension.class)
class EcommerceEventAddressConverterTest {

    public static final String METRIC_MESSAGE = "Metrics counter is incremented";
    public static final String METRICS_NAME = "order.count";
    public static final String METRIC_TAG_MESSAGE = "Metric counter : same address tag counter is incremented";
    public static final String SHIPPING_METHOD = "STANDARD";
    private static final Carrier CARRIER = Carrier.builder()
        .key("CARRIER")
        .build();

    private final MeterRegistry meterRegistry = new SimpleMeterRegistry();
    private final AddressConverterFinderService addressConverterFinderService = new AddressConverterFinderService();
    private final ZoneId timeZone = ZoneId.of("UTC");
    private EcommerceEventConverter converter;
    private Tags tags = Tags.of("carrier", OrderPartsCreatedGenerator.CARRIER, "shippingcountry", "NL");
    @Mock
    private CarrierMappingProperties carrierMappingProperties;

    @BeforeEach
    void setUp() {
        addressConverterFinderService.put("NL_*_*", new NetherlandsAddressService());
        addressConverterFinderService.put("NL_DHL_PICKUP", new NetherlandsDHLPickupAddressService());
        addressConverterFinderService.put("NL_POSTNL_PICKUP", new NetherlandsPostNLPickupAddressService());
        addressConverterFinderService.put("DE_*_*", new GermanyAddressService());
        addressConverterFinderService.put("NO_*_*", new NorwayAddressService());
        addressConverterFinderService.put("DE_DHL_LOCKER", new GermanyDHLPackstationAddressService());

        converter = new EcommerceEventConverter(
            Clock.fixed(Instant.EPOCH, timeZone),
            addressConverterFinderService,
            meterRegistry,
            carrierMappingProperties,
            null
        );
    }

    @Test
    void givenEnrichedOrderWithParcelLocker_whenConvertedToOrderCreation_thenResultHasTwoAddressesWithType() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        orderPartsCreated.getShippingInformation().withParcelLocker(COMPANY_NAME);
        orderPartsCreated.withOrderDetails(
            new OrderDetails()
                .withCarrierVariant("LOCKER")
                .withShippingFees(SHIPPING_FEES)
                .withCarrier(OrderPartsCreatedGenerator.CARRIER)
                .withShippingMethod(SHIPPING_METHOD)
        );
        var enrichedOrderPartsCreated = EnrichedOrderPartsCreated.builder()
            .orderPartsCreated(orderPartsCreated)
            .productsInformation(OrderPartsCreatedGenerator.generateProductInformation())
            .build();
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act & assert
        assertThatThrownBy(() -> converter.convertToOrderCreation(enrichedOrderPartsCreated))
            .isInstanceOf(AddressNotImplementToCountry.class)
            .hasMessage("Address conversion method forLocker not implement to country NL");
    }

    @Test
    void givenEnrichedOrderWithDEParcelLocker_whenConvertedToOrderCreation_thenResultHasOneAddressesWithType() {
        // arrange
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        orderPartsCreated.getShippingInformation().withParcelLocker(COMPANY_NAME);
        orderPartsCreated.withOrderDetails(
            new OrderDetails()
                .withCarrierVariant("HOME")
                .withShippingFees(SHIPPING_FEES)
                .withCarrier(OrderPartsCreatedGenerator.CARRIER)
                .withShippingMethod(SHIPPING_METHOD)
        );
        var enrichedOrderPartsCreated = EnrichedOrderPartsCreated.builder()
            .orderPartsCreated(orderPartsCreated)
            .productsInformation(OrderPartsCreatedGenerator.generateProductInformation())
            .build();
        orderPartsCreated.getShippingInformation().getShippingAddress().withCountry("DE");
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        var orderCreation = converter.convertToOrderCreation(enrichedOrderPartsCreated);

        // assert
        var consumerAddresses = orderCreation.getConsumer().getAddresses();
        assertThat(consumerAddresses)
            .as("Only one consumer address")
            .hasSize(1);
        // assert address type to be postal address
        assertThat(consumerAddresses)
            .extracting(ConsumerAddress::getAddressType)
            .as("Address type is postal address")
            .containsExactly(ConsumerAddress.AddressType.POSTAL_ADDRESS);
    }

    private EnrichedOrderPartsCreated getEnrichedOrderPartsCreated() {
        var orderPartsCreated = OrderPartsCreatedGenerator.generate();
        orderPartsCreated.withOrderDetails(
            new OrderDetails()
                .withCarrierVariant("PICKUP")
                .withCarrier(COMPANY_NAME)
                .withShippingFees(SHIPPING_FEES)
                .withCarrier(OrderPartsCreatedGenerator.CARRIER)
                .withShippingMethod(SHIPPING_METHOD)
        );
        return EnrichedOrderPartsCreated.builder()
            .orderPartsCreated(orderPartsCreated)
            .productsInformation(OrderPartsCreatedGenerator.generateProductInformation())
            .build();
    }

    @Test
    void givenEnrichedOrderWithPickup_whenConvertedToOrderCreation_thenResultHasTwoAddressesWithType() {
        // arrange
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        var orderCreation = converter.convertToOrderCreation(getEnrichedOrderPartsCreated());

        // assert
        var consumerAddresses = orderCreation.getConsumer().getAddresses();
        assertConsumerAddress(consumerAddresses, 2);
        //extracting company name and address type and assert
        assertThat(consumerAddresses)
            .extracting(ConsumerAddress::getCompanyName, ConsumerAddress::getAddressType)
            .as("Company name and address type matches")
            .containsExactly(
                Tuple.tuple(null, INVOICE_ADDRESS),
                Tuple.tuple(OrderPartsCreatedGenerator.CARRIER, ConsumerAddress.AddressType.PARCEL_LOCKER)
            );
    }

    @Test
    void givenEnrichedOrderWithPickup_whenConvertedToOrderCreation_thenResultHasMetricsReportedSameAddress() {
        // arrange
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        converter.convertToOrderCreation(getEnrichedOrderPartsCreated());

        // assert
        assertThat(meterRegistry.get(METRICS_NAME).counter().count())
            .as(METRIC_MESSAGE)
            .isEqualTo(1);
        assertThat(meterRegistry.counter(METRICS_NAME, tags).count())
            .as(METRIC_TAG_MESSAGE)
            .isEqualTo(1);
    }

    @Test
    void givenEnrichedOrderWithPickup_whenConvertedToOrderCreation_thenResultHasMetricsReportedDiffAddress() {
        // arrange
        var enrichedOrderPartsCreated = getEnrichedOrderPartsCreated();
        enrichedOrderPartsCreated.getOrderPartsCreated().getShippingInformation().withShippingAddress(
            new Address().withCountry("NL").withFirstName("Changed").withLastName(LAST_NAME)
        );
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        converter.convertToOrderCreation(enrichedOrderPartsCreated);

        assertThat(meterRegistry.get(METRICS_NAME).counter().count())
            .as(METRIC_MESSAGE)
            .isEqualTo(1);
        assertThat(meterRegistry.counter(METRICS_NAME, tags).count())
            .as(METRIC_TAG_MESSAGE)
            .isEqualTo(1);
    }

    @Test
    void givenEnrichedOrderWithPickupWithFLNull_whenConvertedToOrderCreation_thenResultHasMetricsReportedDiffAddress() {
        // arrange
        var enrichedOrderPartsCreated = getEnrichedOrderPartsCreated();
        enrichedOrderPartsCreated.getOrderPartsCreated().getShippingInformation().withShippingAddress(
            new Address().withCountry("NL").withFirstName(null).withLastName(null)
        );
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        converter.convertToOrderCreation(enrichedOrderPartsCreated);

        assertThat(meterRegistry.get(METRICS_NAME).counter().count())
            .as(METRIC_MESSAGE)
            .isEqualTo(1);
        assertThat(meterRegistry.counter(METRICS_NAME, tags).count())
            .as(METRIC_TAG_MESSAGE)
            .isEqualTo(1);
    }

    @Test
    void givenEnrichedOrderWithPickupWithLNAmeNull_whenConvertedToOrderCreation_thenResultHasMetricsReportedDiffAddress() {
        // arrange
        var enrichedOrderPartsCreated = getEnrichedOrderPartsCreated();
        enrichedOrderPartsCreated.getOrderPartsCreated().getShippingInformation().withShippingAddress(
            new Address().withCountry("NL").withFirstName(FIRST_NAME).withLastName(null)
        );
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        converter.convertToOrderCreation(enrichedOrderPartsCreated);

        assertThat(meterRegistry.get(METRICS_NAME).counter().count())
            .as(METRIC_MESSAGE)
            .isEqualTo(1);
        assertThat(meterRegistry.counter(METRICS_NAME, tags).count())
            .as(METRIC_TAG_MESSAGE)
            .isEqualTo(1);
    }

    @Test
    void givenEnrichedOrderWithPickupWithFNameNull_whenConvertedToOrderCreation_thenResultHasMetricsReported() {
        // arrange
        var enrichedOrderPartsCreated = getEnrichedOrderPartsCreated();
        enrichedOrderPartsCreated.getOrderPartsCreated().getShippingInformation().withShippingAddress(
            new Address().withCountry("NL").withFirstName(FIRST_NAME).withLastName(LAST_NAME)
        );
        enrichedOrderPartsCreated.getOrderPartsCreated().getCustomerInformation().getBillingAddress().setFirstName(null);
        when(carrierMappingProperties.getCarrier(any())).thenReturn(CARRIER);

        // act
        converter.convertToOrderCreation(enrichedOrderPartsCreated);

        assertThat(meterRegistry.get(METRICS_NAME).counter().count())
            .as(METRIC_MESSAGE)
            .isEqualTo(1);
        assertThat(meterRegistry.counter(METRICS_NAME, tags).count())
            .as(METRIC_TAG_MESSAGE)
            .isEqualTo(1);
    }

    protected void assertConsumerAddress(List<ConsumerAddress> consumerAddresses, int expectedSize) {
        assertThat(consumerAddresses)
            .as("Only one consumer address")
            .hasSize(expectedSize);
        assertThat(consumerAddresses)
            .as("consumer address")
            .allSatisfy(consumerAddress -> {
                assertThat(consumerAddress.getFirstName())
                    .as("First names matches")
                    .isEqualTo(FIRST_NAME);
                assertThat(consumerAddress.getLastName())
                    .as("Last names matches")
                    .isEqualTo(LAST_NAME);
                assertThat(consumerAddress.getStreet())
                    .as("Street matches")
                    .isEqualTo(ADDRESS);
                assertThat(consumerAddress.getHouseNumber())
                    .as("House number matches")
                    .isEqualTo(HOUSE_NUMBER);
                assertThat(consumerAddress.getPostalCode())
                    .as("Postal code matches")
                    .isEqualTo(ZIPCODE);
                assertThat(consumerAddress.getCity())
                    .as("City matches")
                    .isEqualTo(CITY);
                assertThat(consumerAddress.getCountry())
                    .as("Country matches")
                    .isEqualTo(COUNTRY);
            })
            .filteredOn(consumerAddress -> consumerAddress.getAddressType() != INVOICE_ADDRESS)
            .singleElement().satisfies(consumerAddress -> {
                assertThat(consumerAddress.getPhoneNumbers())
                    .as("Object has only one phone number")
                    .hasSize(1);
                assertThat(consumerAddress.getPhoneNumbers())
                    .as("Phone number matches")
                    .hasSize(1)
                    .extracting("type", "value")
                    .contains(Tuple.tuple("MOBILE", OrderPartsCreatedGenerator.PHONE_NUMBER));
            });
    }
}
