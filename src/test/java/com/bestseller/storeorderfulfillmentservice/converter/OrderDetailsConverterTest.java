package com.bestseller.storeorderfulfillmentservice.converter;

import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.ArticleAttribute;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.PackJobCreated;
import com.bestseller.storeorderfulfillmentservice.configuration.TemplateCarrierMappingProperties;
import com.bestseller.storeorderfulfillmentservice.model.order.OrderDetails;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import util.PackJobCreatedPayloadGenerator;

import java.math.BigDecimal;
import java.time.Clock;
import java.time.ZoneId;
import java.util.ArrayList;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderDetailsConverterTest {

    @Mock
    private Clock clock;

    @Mock
    private TemplateCarrierMappingProperties templateCarrierMappingProperties;

    @InjectMocks
    private OrderDetailsConverter orderDetailsConverter;

    @Test
    void convert_givenValidPayload_convertedObjectReturned() {
        // arrange
        when(clock.getZone()).thenReturn(ZoneId.of("UTC"));
        var payload = PackJobCreatedPayloadGenerator.generate();

        // act
        var orderDetails = orderDetailsConverter.convert(payload);

        // assert
        assertRootOrderDetails(payload, orderDetails);

        assertCustomerDetails(orderDetails);

        assertThat(orderDetails.getStoreDetails().getFacilityReference()).as("Shipping price matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.FACILITY_REF);

        assertOrderLines(orderDetails);
    }

    @Test
    void convert_givenPayloadWithLength_convertedObjectReturned() {
        // arrange
        var sizeAndLength = "32~30";
        when(clock.getZone()).thenReturn(ZoneId.of("UTC"));
        var payload = PackJobCreatedPayloadGenerator.generate();
        var sizeAndLengthAttribute = ArticleAttribute
            .builder()
            .key("SIZE~LENGTH")
            .value(sizeAndLength)
            .build();
        var attributes = new ArrayList<>(payload.getLineItems().get(0).getArticle().getAttributes());
        attributes.add(sizeAndLengthAttribute);
        payload.getLineItems().get(0).getArticle().setAttributes(attributes);

        // act
        var orderDetails = orderDetailsConverter.convert(payload);

        // assert
        assert orderDetails != null;
        assertThat(orderDetails.getOrderLines().get(0).size()).as("Size and length matches")
            .isEqualTo(sizeAndLength);
    }

    private void assertOrderLines(OrderDetails orderDetails) {
        assertThat(orderDetails.getOrderLines()).as("2 Order lines")
            .hasSize(2);

        var orderLine = orderDetails.getOrderLines().get(0);
        assertThat(orderLine.description()).as("Description matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.BLUE_JEANS_TITLE);
        assertThat(orderLine.ean()).as("EAN matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.EAN_1);
        assertThat(orderLine.styleOption()).as("Style matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.BLUE_DENIM_STYLE_OPTION);
        assertThat(orderLine.quantity()).as("Quantity matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.QUANTITY_EAN_1);
        assertThat(orderLine.price()).as("Price matches")
            .isEqualTo(new BigDecimal("34.59"));

        orderLine = orderDetails.getOrderLines().get(1);
        assertThat(orderLine.description()).as("Description matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.PINK_BLUE_JEANS_TITLE);
        assertThat(orderLine.ean()).as("EAN matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.EAN_2);
        assertThat(orderLine.styleOption()).as("Style matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.PINK_BLUE_DENIM_STYLE_OPTION);
        assertThat(orderLine.quantity()).as("Quantity matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.QUANTITY_EAN_2);
        assertThat(orderLine.price()).as("Price matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.PRICE_EAN_2);
    }

    private void assertCustomerDetails(OrderDetails orderDetails) {
        assertThat(orderDetails.getCustomerDetails().street()).as("Customer street matches")
            .isEqualTo("PandoraStraat");
        assertThat(orderDetails.getCustomerDetails().houseNumber()).as("Customer house number matches")
            .isEqualTo("78D");
        assertThat(orderDetails.getCustomerDetails().city()).as("Customer city matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.CITY);
        assertThat(orderDetails.getCustomerDetails().country()).as("Customer country matches")
            .isEqualTo("Germany");
        assertThat(orderDetails.getCustomerDetails().postalCode()).as("Customer postal code matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.POSTAL_CODE);
        assertThat(orderDetails.getCustomerDetails().firstName()).as("Customer first name matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.FIRST_NAME);
        assertThat(orderDetails.getCustomerDetails().lastname()).as("Customer last name matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.LAST_NAME);
    }

    private void assertRootOrderDetails(PackJobCreated payload, OrderDetails orderDetails) {
        assertThat(orderDetails.getProcessId()).as("Process is matches")
            .isEqualTo(payload.getProcessId());
        assertThat(orderDetails.getOrderNumber()).as("Order number matches")
            .isEqualTo(payload.getTenantOrderId());
        assertThat(orderDetails.getPlacementDate()).as("Placement date matches")
            .isEqualTo("01.02.2023");
        assertThat(orderDetails.getShipmentDate()).as("Shipment date matches")
            .isEqualTo("03.02.2023");
        assertThat(orderDetails.getItemsPrice()).as("Items total price matches")
            .isEqualTo(new BigDecimal("206.34"));
        assertThat(orderDetails.getPickJobReference()).as("Pick job matches")
            .isEqualTo(PackJobCreatedPayloadGenerator.PICK_JOB_REF);

        // These 2 fields are defined in the enrichment.
        assertThat(orderDetails.getTotalPrice()).as("Total price is null")
            .isNull();
        assertThat(orderDetails.getShippingPrice()).as("Shipping price is null")
            .isNull();
    }

}
