package com.bestseller.storeorderfulfillmentservice.service;

import com.bestseller.fulfillmenttoolsclient.configuration.FulfillmentToolsConfig;
import com.bestseller.fulfillmenttoolsclient.service.FulfillmentToolsAuthService;
import com.bestseller.storeorderfulfillmentservice.exception.fft.OrderCreationException;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerInformation;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.OrderCreation;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.process.ProcessRequest;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.FulfillmentToolsService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.http.Fault;
import com.github.tomakehurst.wiremock.http.RequestMethod;
import com.github.tomakehurst.wiremock.junit5.WireMockRuntimeInfo;
import com.github.tomakehurst.wiremock.junit5.WireMockTest;
import com.github.tomakehurst.wiremock.matching.AnythingPattern;
import com.github.tomakehurst.wiremock.matching.RequestPatternBuilder;
import com.github.tomakehurst.wiremock.verification.LoggedRequest;
import lombok.SneakyThrows;
import org.eclipse.jetty.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.time.Duration;
import java.util.List;

import static com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder.responseDefinition;
import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.anyUrl;
import static com.github.tomakehurst.wiremock.client.WireMock.containing;
import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.atIndex;
import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@WireMockTest
class FulfillmentToolsServiceTest {

    private static final String API_FACILITIES = "/api/facilities/";

    private static final String API_PROCESSES = "/api/processes/%s/documents";

    private static final String API_PARCELS = "/api/parcels/";

    private static final String API_CARRIERS = "/api/carriers/";

    private static final String API_ORDERS = "/api/orders/";
    private static final String FACILITY_REF = "123";
    private static final String PARCEL_REF = "456";

    private static final String CARRIER_REF = "499d93f1-1c3c-4e0f-a9a9-5a06d43b9832";

    private static final String CONTENT_TYPE = "Content-Type";
    private static final String APPLICATION_JSON = "application/json";
    private static final String A_BEARER_TOKEN = "aBearerToken";
    private static final String AUTHORIZATION = "Authorization";
    private static final String ERROR = "ERROR";

    @Mock
    private FulfillmentToolsAuthService fulfillmentToolsAuthService;

    private FulfillmentToolsService fulfillmentToolsService;

    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp(WireMockRuntimeInfo wmRuntimeInfo) {
        WebClient fulfillmentToolsClient = spy(new FulfillmentToolsConfig()
            .fulfillmentToolsClient(wmRuntimeInfo.getHttpBaseUrl()));
        fulfillmentToolsService = new FulfillmentToolsService(fulfillmentToolsAuthService,
            fulfillmentToolsClient,
            Duration.parse("PT0.1s"));
        when(fulfillmentToolsAuthService.getToken()).thenReturn(A_BEARER_TOKEN);
    }

    @Test
    void getFacilityId_givenValidLocationChainCode_validFacilityIdIsReturned() {
        // arrange
        var facilityResponse = """
            {
                "tenantFacilityId": "9836"
            }
            """;

        stubFor(get(urlPathEqualTo(API_FACILITIES + FACILITY_REF))
            .withHeader(CONTENT_TYPE, equalTo(APPLICATION_JSON))
            .withHeader(AUTHORIZATION, containing(A_BEARER_TOKEN))
            .willReturn(responseDefinition()
                .withHeader(CONTENT_TYPE, APPLICATION_JSON)
                .withBody(facilityResponse)
                .withStatus(HttpStatus.Code.OK.getCode())));
        // act
        var facility = fulfillmentToolsService.getFacility(FACILITY_REF);

        // assert
        assertThat(facility.tenantFacilityId())
            .as("Facility Id was returned")
            .isEqualTo("9836");
    }

    @Test
    void getReturnLabelId_givenValidParcelId_validReturnLabelIdIsReturned() {
        // arrange
        var parcelResponse = """
            {
                "result": { "returnLabelId": "label-123" }
            }
            """;

        stubFor(get(urlPathEqualTo(API_PARCELS + PARCEL_REF))
            .withHeader(CONTENT_TYPE, equalTo(APPLICATION_JSON))
            .withHeader(AUTHORIZATION, containing(A_BEARER_TOKEN))
            .willReturn(responseDefinition()
                .withHeader(CONTENT_TYPE, APPLICATION_JSON)
                .withBody(parcelResponse)
                .withStatus(HttpStatus.Code.OK.getCode())));
        // act
        var parcel = fulfillmentToolsService.getParcel(PARCEL_REF);

        // assert
        assertThat(parcel.parcel().returnLabelId())
            .as("Return label id was returned")
            .isEqualTo("label-123");
    }

    @Test
    void postDocumentToProcess_givenValidDocument_fileIsPublished() throws IOException {
        // arrange
        String fileName = "fileName.pdf";
        var processId = "569033D6-6436-4DA7-956B-71F62A0D347F";
        var data = Files.newInputStream(File.createTempFile("test", "pdf").toPath());

        stubFor(post(urlPathEqualTo(API_PROCESSES.formatted(processId)))
            .withHeader(CONTENT_TYPE, equalTo(APPLICATION_JSON))
            .withHeader(AUTHORIZATION, containing(A_BEARER_TOKEN))
            .withRequestBody(new AnythingPattern())
            .willReturn(responseDefinition()
                .withHeader(CONTENT_TYPE, APPLICATION_JSON)
                .withStatus(HttpStatus.Code.OK.getCode())));

        // act
        fulfillmentToolsService.postDocumentToProcess(data, processId, fileName);

        // assert
        LoggedRequest loggedRequest = await("POST call has been registered in the wiremock journal")
            .until(
                () -> WireMock.findAll(RequestPatternBuilder.newRequestPattern(RequestMethod.POST, any()))
                    .stream().findFirst(),
                optLoggedRequest -> optLoggedRequest.isPresent())
            .get();

        var processRequest = parseJson(loggedRequest.getBodyAsString(), ProcessRequest.class);

        assertThat(processRequest.getSection())
            .as("Section is correct")
            .isEqualTo("PACKJOB");

        assertThat(processRequest.getType())
            .as("Type is correct")
            .isEqualTo("PDF");

        assertThat(processRequest.getFile().getName())
            .as("File name is correct")
            .isEqualTo(fileName);
    }

    @Test
    void getPickJob_givenValidPickJobId_validReturnPickJobIsReturned() {
        // arrange
        final int quantity1 = 42;
        final int quantity2 = 69;
        String ean1 = "5715371213830";
        String ean2 = "5715371069697";

        String pickJobRef = "pickpick-pick-pick-pick-jobjobjobjob";
        String pickJobUrl = "/api/pickjobs/pickpick-pick-pick-pick-jobjobjobjob";

        stubFor(get(urlPathEqualTo(pickJobUrl))
            .withHeader(CONTENT_TYPE, equalTo(APPLICATION_JSON))
            .withHeader(AUTHORIZATION, containing(A_BEARER_TOKEN))
            .willReturn(responseDefinition()
                .withHeader(CONTENT_TYPE, APPLICATION_JSON)
                .withBody("""
                    {
                        "pickLineItems": [
                            {
                                "quantity": %d,
                                "article": {
                                    "tenantArticleId": "%s"
                                }
                            },
                            {
                                "quantity": %d,
                                "article": {
                                    "tenantArticleId": "%s"
                                }
                            }
                        ]
                    }
                    """.formatted(quantity1, ean1, quantity2, ean2))
                .withStatus(HttpStatus.Code.OK.getCode())));
        // act
        var pickJob = fulfillmentToolsService.getPickJob(pickJobRef);

        // assert
        assertThat(pickJob.pickLineItems())
            .as("line items")
            .hasSize(2)
            .satisfies(item -> {
                assertThat(item.getArticle().getEan())
                    .as("EAN")
                    .isEqualTo(ean1);
                assertThat(item.getQuantity())
                    .as("quantity")
                    .isEqualTo(quantity1);
            }, atIndex(0))
            .satisfies(item -> {
                assertThat(item.getArticle().getEan())
                    .as("EAN")
                    .isEqualTo(ean2);
                assertThat(item.getQuantity())
                    .as("quantity")
                    .isEqualTo(quantity2);
            }, atIndex(1));
    }

    @Test
    void getCarrier_givenValidCarrierId_validReturnCarrierIsReturned() {
        // arrange
        var parcelResponse = """
                {
                    "name": "postnl"
                }
            """;

        stubFor(get(urlPathEqualTo(API_CARRIERS + CARRIER_REF))
            .inScenario("getCarrier_givenValidCarrierId_validReturnCarrierIsReturned")
            .withHeader(CONTENT_TYPE, equalTo(APPLICATION_JSON))
            .withHeader(AUTHORIZATION, containing(A_BEARER_TOKEN))
            .willReturn(aResponse().withFault(Fault.CONNECTION_RESET_BY_PEER))
            .willSetStateTo(ERROR));

        stubFor(get(urlPathEqualTo(API_CARRIERS + CARRIER_REF))
            .inScenario("getCarrier_givenValidCarrierId_validReturnCarrierIsReturned")
            .whenScenarioStateIs(ERROR)
            .withHeader(CONTENT_TYPE, equalTo(APPLICATION_JSON))
            .withHeader(AUTHORIZATION, containing(A_BEARER_TOKEN))
            .willReturn(responseDefinition()
                .withHeader(CONTENT_TYPE, APPLICATION_JSON)
                .withStatus(HttpStatus.Code.BAD_GATEWAY.getCode()))
            .willSetStateTo("ERROR 1"));

        stubFor(get(urlPathEqualTo(API_CARRIERS + CARRIER_REF))
            .inScenario("getCarrier_givenValidCarrierId_validReturnCarrierIsReturned")
            .whenScenarioStateIs("ERROR 1")
            .withHeader(CONTENT_TYPE, equalTo(APPLICATION_JSON))
            .withHeader(AUTHORIZATION, containing(A_BEARER_TOKEN))
            .willReturn(responseDefinition()
                .withHeader(CONTENT_TYPE, APPLICATION_JSON)
                .withBody(parcelResponse)
                .withStatus(HttpStatus.Code.OK.getCode())));
        // act
        var carrier = fulfillmentToolsService.getCarrier(CARRIER_REF);

        // assert
        assertThat(carrier.name())
            .as("Carrier name was returned")
            .isEqualTo("postnl");
    }

    @Test
    void isANewOrder_givenValidTenantOrderId_orderExists() {
        // arrange
        var orderId = "TB55126970";
        var fulfillmentToolsOrderId = "652b163b-fc9b-4f7d-943f-bca459ae50f6";
        var ordersResponse = """
                {
                     "orders": [
                         {
                             "id": "%s"
                         }
                     ]
                 }
            """.formatted(fulfillmentToolsOrderId);

        stubFor(get(urlPathEqualTo(API_ORDERS))
            .withQueryParam("tenantOrderId", equalTo(orderId))
            .withHeader(CONTENT_TYPE, equalTo(APPLICATION_JSON))
            .withHeader(AUTHORIZATION, containing(A_BEARER_TOKEN))
            .willReturn(responseDefinition()
                .withHeader(CONTENT_TYPE, APPLICATION_JSON)
                .withBody(ordersResponse)
                .withStatus(HttpStatus.Code.OK.getCode())));

        // act & assert
        assertThat(fulfillmentToolsService.isANewOrder(orderId))
            .as("Order was placed")
            .isFalse();
    }

    @Test
    void isANewOrder_givenValidTenantOrderId_orderDoesntExist() {
        // arrange
        var orderId = "TB55126971";
        var ordersResponse = """
                {
                     "orders": []
                 }
            """;

        stubFor(get(urlPathEqualTo(API_ORDERS))
            .withQueryParam("tenantOrderId", equalTo(orderId))
            .withHeader(CONTENT_TYPE, equalTo(APPLICATION_JSON))
            .withHeader(AUTHORIZATION, containing(A_BEARER_TOKEN))
            .willReturn(responseDefinition()
                .withHeader(CONTENT_TYPE, APPLICATION_JSON)
                .withBody(ordersResponse)
                .withStatus(HttpStatus.Code.OK.getCode())));

        // act & assert
        assertThat(fulfillmentToolsService.isANewOrder(orderId))
            .as("Order was placed placed")
            .isTrue();
    }

    @Test
    void placeAnOrder_givenValidOrder_orderIsSent() {
        // arrange
        String orderId = "D1221";
        String pickUpPointCode = "123456";
        var orderCreation = OrderCreation.builder()
            .tenantOrderId(orderId)
            .consumer(ConsumerInformation.builder()
                .addresses(List.of(ConsumerAddress.builder()
                    .additionalAddressInfo(pickUpPointCode)
                    .build()))
                .build())
            .build();

        stubFor(post(anyUrl())
            .willReturn(responseDefinition()
                .withStatus(HttpStatus.Code.OK.getCode())));

        // act
        fulfillmentToolsService.placeAnOrder(orderCreation);

        // assert
        verify(postRequestedFor(urlEqualTo(API_ORDERS))
            .withHeader(CONTENT_TYPE, equalTo(APPLICATION_JSON))
            .withHeader(AUTHORIZATION, containing("Bearer " + A_BEARER_TOKEN))
            .withRequestBody(equalToJson("""
                    {
                      "tenantOrderId": "%s",
                      "consumer": {
                        "addresses": [
                          {
                            "additionalAddressInfo": "%s"
                          }
                        ]
                      }
                    }
                """.formatted(orderId, pickUpPointCode), false, true)));
    }

    @Test
    void placeAnOrder_fulfillmentToolsApiIsDown_errorWasThrown() {
        // arrange
        String orderId = "D1222";
        var orderCreation = OrderCreation.builder()
            .tenantOrderId(orderId)
            .build();
        stubFor(post(anyUrl())
            .inScenario("placeAnOrder_fulfillmentToolsApiIsDown_errorWasThrown")
            .willReturn(responseDefinition()
                .withStatus(HttpStatus.Code.BAD_GATEWAY.getCode()))
            .willSetStateTo(ERROR));

        stubFor(post(anyUrl())
            .inScenario("placeAnOrder_fulfillmentToolsApiIsDown_errorWasThrown")
            .whenScenarioStateIs(ERROR)
            .willReturn(responseDefinition()
                .withStatus(HttpStatus.Code.BAD_REQUEST.getCode())));

        // act
        var exception = assertThrows(
            OrderCreationException.class,
            () -> fulfillmentToolsService.placeAnOrder(orderCreation));

        // assert
        assertThat(exception).as("Exception").hasMessageContaining(orderId);

    }

    @SneakyThrows
    private <T> T parseJson(String json, Class<T> clazz) {
        return objectMapper.readValue(json, clazz);
    }
}
