package com.bestseller.storeorderfulfillmentservice.service.pcs;

import com.bestseller.storeorderfulfillmentservice.configuration.pcs.PcsConfig;
import com.bestseller.storeorderfulfillmentservice.exception.pcs.PcsRequestException;
import com.bestseller.storeorderfulfillmentservice.model.pcs.ProductInformation;
import com.github.tomakehurst.wiremock.junit5.WireMockRuntimeInfo;
import com.github.tomakehurst.wiremock.junit5.WireMockTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.codec.DecodingException;

import java.util.List;
import java.util.Map;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;
import static org.springframework.http.HttpStatus.BAD_REQUEST;
import static org.springframework.http.HttpStatus.GATEWAY_TIMEOUT;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

/**
 * Unit tests for {@link GraphQLQueryService}.
 */
@ExtendWith(MockitoExtension.class)
@WireMockTest
class GraphQLQueryServiceTest {
    private static final String EANS = "eans";
    private static final String TEST = "test";

    private GraphQLQueryService graphQLQueryService;

    @BeforeEach
    void setUp(WireMockRuntimeInfo wmRuntimeInfo) {
        var webClient = new PcsConfig().pcsClient(wmRuntimeInfo.getHttpBaseUrl());
        graphQLQueryService = new GraphQLQueryService(webClient);
    }

    @Test
    void executeQuery_givenValidQueryAndEmptyResult_emptyListIsReturned() {
        // arrange
        stubFor(post(urlEqualTo("/"))
                .willReturn(aResponse()
                        .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                        .withBody("""
                        {
                           "data": {
                             "eans": []
                           }
                         }""")));

        // act
        List<ProductInformation> productsInformation = graphQLQueryService.executeQuery(TEST, Map.of(), EANS, new ParameterizedTypeReference<>() { });

        // assert
        assertThat(productsInformation).as("Array should be empty").isEmpty();
    }

    @Test
    void executeQuery_givenPcsIsRestarting_retriedNoExceptionThrown() {
        // arrange
        stubFor(post(urlEqualTo("/"))
                .willReturn(aResponse()
                        .withStatus(GATEWAY_TIMEOUT.value()))
                .willReturn(aResponse()
                        .withStatus(GATEWAY_TIMEOUT.value()))
                .willReturn(aResponse()
                        .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                        .withBody("""
                        {
                           "data": {
                             "eans": []
                           }
                         }""")));

        // act
        graphQLQueryService.executeQuery(TEST, Map.of(), EANS, new ParameterizedTypeReference<>() { });
    }

    @Test
    void executeQuery_givenValidQueryAndValidResponseWithNonDefinedFields_productInformationIsReturned() {
        var ean = "5711293509020";
        var styleOption = "12059219_BLACK";
        var styleNumber = "12059219";
        var length = "50";
        var imageReferenceUrl = "http://test.bestseller.com/img/test.jpg";
        var color = "BLACK";
        var size = "L";
        var productName = "Basic V-neck Regular Fit T-shirt";
        var ediStyleName = "JJIMIKE JJORIGINAL GE 397 NOOS";
        // arrange - a field productName which is not defined on the model is added to check if it would fail with
        // extra fields
        stubFor(post(urlEqualTo("/"))
                .willReturn(aResponse()
                        .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                        .withBody("""
                        {
                          "data": {
                            "eans": [
                              {
                                "ean": "%s",
                                "styleNumber": "%s",
                                "styleOption": "%s",
                                "productName": "%s",
                                "color": "%s",
                                "size": "%s",
                                "length": "%s",
                                "ediStyleName": "%s",
                                "anotherField": "field",
                                "imageReferencesUrls": [
                                  "%s",
                                  "http://test.bestseller.com/img/test2.jpg",
                                  "http://test.bestseller.com/img/test3.jpg",
                                  "http://test.bestseller.com/img/test4.jpg",
                                  "http://test.bestseller.com/img/test5.jpg",
                                  "http://test.bestseller.com/img/test6.jpg",
                                  "http://test.bestseller.com/img/test7.jpg",
                                  "http://test.bestseller.com/img/test8.jpg"
                                ]
                              }
                            ]
                          }
                        }""".formatted(ean, styleNumber, styleOption, productName, color, size, length, ediStyleName, imageReferenceUrl))));

        String query = """
                query something(vars: Types!) {
                  eans(and so on""";

        // act
        List<ProductInformation> productsInformation =
                graphQLQueryService.executeQuery(query, Map.of(), EANS, new ParameterizedTypeReference<>() { });

        // assert
        assertThat(productsInformation).as("Products Information should contain exactly one result")
                .hasSize(1);
        ProductInformation productInformation = productsInformation.iterator().next();
        assertThat(productInformation.ean()).as("Ean should match")
                .isEqualTo(ean);
    }

    @Test
    void executeQuery_givenValidQueryErrorPresentOnResponse_graphqlExceptionIsThrown() {
        // arrange
        stubFor(post(urlEqualTo("/"))
                .willReturn(aResponse()
                        .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                        .withBody("""
                        {
                           "errors":[
                              {
                                 "message":"Unknown argument \\"ean\\" on field \\"eans\\" of type \\"Query\\". Did you mean \\"eans\\"?",
                                 "locations":[
                                    {
                                       "line":1,
                                       "column":7
                                    }
                                 ]
                              }
                           ]
                        }
                        """)));
        var clazz = new ParameterizedTypeReference<GraphQLResponseWrapper<List<ProductInformation>>>() { };

        // act & assert
        assertThrows(PcsRequestException.class, () ->
                graphQLQueryService.executeQuery(TEST, Map.of(), EANS, clazz));
    }

    @Test
    void executeQuery_givenExceptionDuringRequest_pcsServiceUnavailableExceptionIsThrown() {
        // arrange
        stubFor(post(urlEqualTo("/"))
                .willReturn(aResponse()
                        .withStatus(BAD_REQUEST.value())));
        var clazz = new ParameterizedTypeReference<GraphQLResponseWrapper<List<ProductInformation>>>() { };

        // act & assert
        assertThrows(PcsRequestException.class, () ->
                graphQLQueryService.executeQuery(TEST, Map.of(), EANS, clazz));
    }

    @Test
    void executeQuery_jsonExceptionThrown_pcsClientRequestExceptionIsThrown() {
        // arrange
        stubFor(post(urlEqualTo("/"))
                .willReturn(aResponse()
                        .withHeader(CONTENT_TYPE, APPLICATION_JSON_VALUE)
                        .withBody("{ almost: JSON")));
        var clazz = new ParameterizedTypeReference<GraphQLResponseWrapper<List<ProductInformation>>>() { };

        // act & assert
        assertThrows(DecodingException.class, () ->
                graphQLQueryService.executeQuery(TEST, Map.of(), EANS, clazz));
    }
}
