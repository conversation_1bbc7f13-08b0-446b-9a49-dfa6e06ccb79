package com.bestseller.storeorderfulfillmentservice.service.pcs;

import com.bestseller.storeorderfulfillmentservice.exception.pcs.PcsValidationException;
import com.bestseller.storeorderfulfillmentservice.model.pcs.ProductInformation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import util.ProductInformationGenerator;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Unit tests for {@link ProductInformationService}.
 */
@ExtendWith(MockitoExtension.class)
class ProductInformationServiceTest {

    private static final String EAN_1 = "5713755361573";
    private static final String EAN_2 = "5713756923909";
    private static final String EAN_3 = "5714497367687";
    private static final Set<String> EANS = new LinkedHashSet<>(List.of(EAN_1, EAN_2, EAN_3));

    @Mock
    private GraphQLQueryService pcsGraphqlService;

    @InjectMocks
    private ProductInformationService productInformationService;

    private List<ProductInformation> productsInformation;

    @Captor
    private ArgumentCaptor<Map<String, Object>> variablesCaptor;

    @BeforeEach
    void setUp() {
        productsInformation = EANS.stream().map(ProductInformationGenerator::createEan).toList();
    }

    @Test
    void getProductInformation_givenValidEansAndPcsResponse_productInformationReturned() {
        // arrange
        var eans = Set.of("5713755361573", "5713756923909", "5714497367687");

        var clazz = new ParameterizedTypeReference<GraphQLResponseWrapper<List<ProductInformation>>>() { };
        when(pcsGraphqlService.executeQuery(anyString(), variablesCaptor.capture(), eq("eans"), eq(clazz)))
                .thenReturn(productsInformation);

        // act
        var productInformation = productInformationService.getProductInformation(eans);

        // assert
        assertThat(productInformation.keySet())
                .as("Product information should have same size than products")
                .containsExactlyInAnyOrderElementsOf(EANS);
        assertThat(productsInformation.stream().map(ProductInformation::ean).collect(Collectors.toList()))
                .as("All EANs should be present on product information")
                .containsExactlyInAnyOrderElementsOf(eans);
        assertThat(variablesCaptor.getValue())
                .as("Variables")
                .containsEntry("EANs", eans)
                .containsEntry("loc", "en-NL");
    }

    @Test
    void getProductInformation_givenMissingEansInPcsResponse_exceptionThrown() {
        // arrange
        var missingProductResponse = productsInformation.stream().skip(1).toList();
        when(pcsGraphqlService.executeQuery(any(), any(), any(), any()))
                .thenReturn(missingProductResponse);

        // act & assert
        assertThrows(PcsValidationException.class, () -> productInformationService.getProductInformation(EANS));
    }
}

