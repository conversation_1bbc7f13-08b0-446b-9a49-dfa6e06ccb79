package com.bestseller.storeorderfulfillmentservice.service;

import com.bestseller.storeorderfulfillmentservice.converter.address.GermanyAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.GermanyDHLPackstationAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.NetherlandsAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.NetherlandsDHLPickupAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.NetherlandsPostNLPickupAddressService;
import com.bestseller.storeorderfulfillmentservice.converter.address.NorwayAddressService;
import com.bestseller.storeorderfulfillmentservice.model.DeliveryType;
import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class AddressConverterFinderServiceTest {

    private AddressConverterFinderService addressConverterFinderService;

    @BeforeEach
    void setup() {
        // arrange
        addressConverterFinderService = new AddressConverterFinderService();
        addressConverterFinderService.put("NL_*_*", new NetherlandsAddressService());
        addressConverterFinderService.put("NL_DHL_PICKUP", new NetherlandsDHLPickupAddressService());
        addressConverterFinderService.put("NL_POSTNL_PICKUP", new NetherlandsPostNLPickupAddressService());
        addressConverterFinderService.put("DE_*_*", new GermanyAddressService());
        addressConverterFinderService.put("NO_*_*", new NorwayAddressService());
        addressConverterFinderService.put("DE_DHL_LOCKER", new GermanyDHLPackstationAddressService());
    }

    @Test
    void getAddressService_givenCountryAndCarrierAndDeliveryType_returnsAddressService() {
        // act
        var addressService = addressConverterFinderService.getAddressService(DeliveryOptionQuery.builder()
            .country("DE")
            .carrier("DHL")
            .deliveryType(DeliveryType.LOCKER)
            .build());

        // assert
        assertThat(addressService).isInstanceOf(GermanyDHLPackstationAddressService.class);
    }
}
