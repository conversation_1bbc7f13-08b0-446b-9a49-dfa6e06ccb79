package com.bestseller.storeorderfulfillmentservice.service;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class BarcodeServiceTest {

    private final BarcodeService barcodeService = new BarcodeService();

    @Test
    void generateBarcode() {
        // arrange
        var orderId = "OL12231312";

        // act
        String barcode = barcodeService.generateCode128Barcode(orderId);

        // assert
        final int stringLength = 186;
        assertThat(barcode)
            .contains("data:image/png;base64")
            .hasSize(stringLength);
    }

}
