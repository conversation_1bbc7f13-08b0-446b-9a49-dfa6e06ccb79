package com.bestseller.storeorderfulfillmentservice.service;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.SdkClientException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.catchThrowable;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.doThrow;

@ExtendWith(MockitoExtension.class)
class S3ServiceTest {

    private static final String BUCKET = "test-bucket";

    private static final String JSON_CONTENT = "{\"key\":\"value\"}";
    private static final String JSON_DIRECTORY = "json-directory";
    private static final String JSON_FILE_NAME = "json-file";

    @Mock
    private AmazonS3 amazonS3;

    @InjectMocks
    private S3Service s3Service;

    @Captor
    private ArgumentCaptor<ObjectMetadata> objectMetadataArgumentCaptor;

    @Captor
    private ArgumentCaptor<ByteArrayInputStream> inputStreamCaptor;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(s3Service, "bucket", BUCKET, String.class);
    }

    @Test
    void uploadDocument_givenValidInputStream_amazonServiceIsCalled() throws IOException {
        // arrange
        var document = new ByteArrayInputStream(Files.newInputStream(File.createTempFile("test", ".txt").toPath()).readAllBytes());
        var remoteDirectory = "directory";
        var fileName = "fileName";

        // act
        s3Service.uploadDocument(document, remoteDirectory, fileName);

        // assert
        verify(amazonS3).putObject(
                eq(BUCKET),
                eq(Paths.get(remoteDirectory, fileName).toString()),
                eq(document),
                objectMetadataArgumentCaptor.capture());
        assertThat(objectMetadataArgumentCaptor.getValue().getContentLength())
                .as("Size matches")
                .isEqualTo(document.available());
    }

    @Test
    void uploadJsonContent_givenValidContent_amazonServiceIsCalled() throws IOException {
        // arrange
        byte[] jsonContent = JSON_CONTENT.getBytes();

        // act
        s3Service.uploadJsonContent(jsonContent, JSON_DIRECTORY, JSON_FILE_NAME);

        // assert
        verify(amazonS3).putObject(
                eq(BUCKET),
                eq(Paths.get(JSON_DIRECTORY, JSON_FILE_NAME).toString()),
                inputStreamCaptor.capture(),
                objectMetadataArgumentCaptor.capture());

        ObjectMetadata metadata = objectMetadataArgumentCaptor.getValue();
        assertThat(metadata.getContentLength()).as("Content length matches").isEqualTo(jsonContent.length);
        assertThat(metadata.getContentType()).as("Content type is JSON").isEqualTo("application/json");

        // Verify the actual content of the ByteArrayInputStream
        ByteArrayInputStream capturedStream = inputStreamCaptor.getValue();
        byte[] capturedContent = new byte[jsonContent.length];
        capturedStream.read(capturedContent);
        assertThat(capturedContent).as("JSON content matches").isEqualTo(jsonContent);
    }

    @Test
    void uploadJsonContent_whenAmazonS3Exception_shouldHandleGracefully() {
        // arrange
        byte[] jsonContent = JSON_CONTENT.getBytes();

        doThrow(new AmazonS3Exception("Test S3 exception"))
                .when(amazonS3).putObject(anyString(), anyString(), any(ByteArrayInputStream.class), any(ObjectMetadata.class));

        // act
        Throwable thrown = catchThrowable(() ->
            s3Service.uploadJsonContent(jsonContent, JSON_DIRECTORY, JSON_FILE_NAME));

        // assert that the method handled the exception and didn't propagate it
        assertThat(thrown).as("Exception should be handled internally").isNull();
    }

    @Test
    void uploadJsonContent_whenSdkClientException_shouldHandleGracefully() {
        // arrange
        byte[] jsonContent = JSON_CONTENT.getBytes();

        doThrow(new SdkClientException("Test SDK exception"))
                .when(amazonS3).putObject(anyString(), anyString(), any(ByteArrayInputStream.class), any(ObjectMetadata.class));

        // act
        Throwable thrown = catchThrowable(() ->
            s3Service.uploadJsonContent(jsonContent, JSON_DIRECTORY, JSON_FILE_NAME));

        // assert that the method handled the exception and didn't propagate it
        assertThat(thrown).as("Exception should be handled internally").isNull();
    }
}
