package com.bestseller.storeorderfulfillmentservice.service;

import com.bestseller.storeorderfulfillmentservice.configuration.brand.BrandPropertiesConfiguration;
import com.bestseller.storeorderfulfillmentservice.configuration.localization.LocalizationConfig;
import com.bestseller.storeorderfulfillmentservice.exception.HtmlToPdfParsingException;
import com.bestseller.storeorderfulfillmentservice.model.brand.BrandProperty;
import com.bestseller.storeorderfulfillmentservice.model.localization.Localization;
import com.bestseller.storeorderfulfillmentservice.model.order.CustomerDetails;
import com.bestseller.storeorderfulfillmentservice.model.order.OrderDetails;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;

import static freemarker.template.Configuration.VERSION_2_3_0;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FileRenderingServiceTest {

    @InjectMocks
    private FileRenderingService fileRenderingService;

    @Mock
    private Configuration templateConfiguration;
    @Mock
    private LocalizationConfig localizationConfig;
    @Mock
    private BrandPropertiesConfiguration brandPropertiesConfiguration;
    @Mock
    private BarcodeService barcodeService;
    @Mock
    private FontService fontService;

    @BeforeEach
    void setUp() {
        when(localizationConfig.getLocalizationByCountry(any()))
            .thenReturn(new Localization());
        when(brandPropertiesConfiguration.getBrandProperty(any()))
            .thenReturn(BrandProperty.builder()
                .name("Brand")
                .website("www.brand.com")
                .logoUrl("image.png")
                .build());
        when(barcodeService.generateCode128Barcode(any()))
            .thenReturn("data:image/png;base64,barcode");
    }

    @Test
    void writeInvoice_givenValidTemplateAndData_fileReturnedWithTransformedParameters() throws IOException {
        // arrange
        final var htmlTemplate = "invoice.ftlh";
        final var orderNumber = "0123456789";
        Configuration cfg = new Configuration(Configuration.getVersion());
        String html = """
            <!DOCTYPE html>
            <html>
            <head>
            <title>Page Title</title>
            </head>
            <body>
            <h1>${data.orderNumber}</h1>
            </body>
            </html>
            """;
        Template template = new Template(htmlTemplate, html, cfg);
        final var country = "NL";
        when(templateConfiguration.getTemplate(htmlTemplate)).thenReturn(template);
        var outputStream = new ByteArrayOutputStream();
        ArgumentCaptor<PdfRendererBuilder> builderCaptor = ArgumentCaptor.forClass(PdfRendererBuilder.class);

        // act
        fileRenderingService.writeInvoice(OrderDetails.builder()
            .orderNumber(orderNumber)
            .customerDetails(CustomerDetails.builder()
                .countryCode(country)
                .build())
            .build(), outputStream);

        // assert
        verify(fontService).defineFonts(builderCaptor.capture());
        assertThat(builderCaptor.getValue()).isNotNull();
        assertThat(outputStream.toByteArray())
            .as("output bytes")
            .startsWith("%PDF-".getBytes())
            .containsSequence("Page Title".getBytes());
    }

    @Test
    void writeReturnSlip_givenValidTemplateAndData_fileReturnedWithTransformedParameters() throws IOException {
        // arrange
        when(templateConfiguration.getTemplate(any()))
            .thenReturn(new Template("foo", "<html></html>", new Configuration(Configuration.getVersion())));

        var outputStream = new ByteArrayOutputStream();
        ArgumentCaptor<PdfRendererBuilder> builderCaptor = ArgumentCaptor.forClass(PdfRendererBuilder.class);

        // act
        fileRenderingService.writeReturnSlip(OrderDetails.builder()
            .orderNumber("1234")
            .customerDetails(CustomerDetails.builder()
                .countryCode("NL").build())
            .build(), outputStream);

        // assert
        verify(fontService).defineFonts(builderCaptor.capture());
        assertThat(builderCaptor.getValue()).isNotNull();
        assertThat(outputStream.toByteArray())
            .as("output bytes")
            .startsWith("%PDF-".getBytes());

        verify(templateConfiguration).getTemplate("return-slip.ftlh");
    }

    @Test
    void writeInvoice_givenBadTemplate_throwException() throws IOException {
        // arrange
        final var orderNumber = "1234566789";
        final var country = "NL";
        when(templateConfiguration.getTemplate(anyString()))
            .thenReturn(new Template("foo", """
                <html>
                <!-- FIXME close html tag -->
                """, new Configuration(VERSION_2_3_0)));
        ArgumentCaptor<PdfRendererBuilder> builderCaptor = ArgumentCaptor.forClass(PdfRendererBuilder.class);

        // action
        HtmlToPdfParsingException exception = assertThrows(HtmlToPdfParsingException.class,
            () -> fileRenderingService.writeInvoice(OrderDetails.builder()
                    .orderNumber(orderNumber)
                    .customerDetails(CustomerDetails.builder()
                        .countryCode(country).build())
                    .build(),
                OutputStream.nullOutputStream()));

        // assert
        verify(fontService).defineFonts(builderCaptor.capture());
        assertThat(builderCaptor.getValue()).isNotNull();
        assertThat(exception)
            .as("Message contains file exception message")
            .hasMessageContaining(orderNumber);
    }
}
