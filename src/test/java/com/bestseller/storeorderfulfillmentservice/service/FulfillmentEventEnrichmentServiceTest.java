package com.bestseller.storeorderfulfillmentservice.service;

import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.dispatched.HandoverJobHandedOver;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobAborted;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobCreated;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobPickingFinished;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedHandoverJobHandedOver;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedPickJobAborted;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedPickJobCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.CustomAttributes;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.Parcel;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.ParcelInformation;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.facility.Facility;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.facility.FacilityAddress;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.picked.PickJob;
import com.bestseller.storeorderfulfillmentservice.model.order.OrderDetails;
import com.bestseller.storeorderfulfillmentservice.model.order.StoreDetails;
import com.bestseller.storeorderfulfillmentservice.model.pcs.EnrichedPickJobPickingFinished;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.FulfillmentToolsService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FulfillmentEventEnrichmentServiceTest {

    public static final String FACILITY_REF = "Esb20gpHBL94X5NdMp3C";
    @Mock
    private FulfillmentToolsService fulfillmentToolsService;

    @InjectMocks
    private FulfillmentEventEnrichmentService fulfillmentEventEnrichmentService;

    private Facility facility = new Facility(
        "1234",
        FacilityAddress.builder()
            .country("XY")
            .city("City")
            .companyName("Jack Moda")
            .houseNumber("1")
            .postalCode("1013 XO")
            .street("Street")
            .build());

    @Test
    void enrichWithFacility_givenPickJob_richEventReturned() {
        // arrange
        String ref = "Esb20gpHBL94X5NdMp3Cfoobar";
        var event = PickJobCreated.builder()
            .facilityRef(ref)
            .build();

        when(fulfillmentToolsService.getFacility(anyString()))
            .thenReturn(facility);

        // act
        EnrichedPickJobCreated richEvent = fulfillmentEventEnrichmentService.enrichWithFacility(event);

        // assert
        assertThat(richEvent.fulfillmentEvent())
            .as("pick job event")
            .isEqualTo(event);
        assertThat(richEvent.facility())
            .as("pick job facility")
            .isEqualTo(facility);

        verify(fulfillmentToolsService).getFacility(ref);
    }

    @Test
    void enrichWithFacility_givenPickJobAborted_richEventReturned() {
        // arrange
        var event = PickJobAborted.builder()
            .facilityRef(FACILITY_REF)
            .build();

        when(fulfillmentToolsService.getFacility(anyString()))
            .thenReturn(facility);

        // act
        EnrichedPickJobAborted richEvent = fulfillmentEventEnrichmentService.enrichWithFacility(event);

        // assert
        assertThat(richEvent.pickJobAborted())
            .as("pickJobAborted event")
            .isEqualTo(event);
        assertThat(richEvent.facility())
            .as("facility in pickJobAborted")
            .isEqualTo(facility);

        verify(fulfillmentToolsService).getFacility(FACILITY_REF);
    }

    @Test
    void enrichWithFacility_givenHandover_richEventReturned() {
        // arrange
        String parcelRef = "2fOge2ZGW54K4TgvDTQw";

        var event = HandoverJobHandedOver.builder()
            .facilityRef(FACILITY_REF)
            .parcelRef(parcelRef)
            .build();
        var parcel = new Parcel(new ParcelInformation("3a186c51d4281acbecf5ed38805b1db92a9d668b"));

        when(fulfillmentToolsService.getFacility(anyString()))
            .thenReturn(facility);
        when(fulfillmentToolsService.getParcel(parcelRef))
            .thenReturn(parcel);

        // act
        EnrichedHandoverJobHandedOver richEvent = fulfillmentEventEnrichmentService.enrichWithFacilityAndParcel(event);

        // assert
        assertThat(richEvent.fulfillmentEvent())
            .as("event")
            .isEqualTo(event);
        assertThat(richEvent.facility())
            .as("facility")
            .isEqualTo(facility);
        assertThat(richEvent.parcel())
            .as("parcel")
            .isEqualTo(parcel);

        verify(fulfillmentToolsService).getFacility(FACILITY_REF);
    }

    @Test
    void enrichWithFacilityDetails_givenOrderDetails_richOrderDetailsReturned() {
        // arrange
        String facilityRef = "AFFADC77-3D20-4A32-8DEF-247F31B46015";

        var orderDetails = OrderDetails.builder()
            .storeDetails(StoreDetails.builder()
                .facilityReference(facilityRef)
                .build())
            .build();

        when(fulfillmentToolsService.getFacility(facilityRef))
            .thenReturn(facility);

        // act
        fulfillmentEventEnrichmentService.enrichWithFacilityDetails(orderDetails);

        // assert
        FacilityAddress address = facility.address();
        StoreDetails storeDetails = orderDetails.getStoreDetails();
        assertThat(storeDetails.getLocationChainCode())
            .as("Location chain code matches")
            .isEqualTo(facility.tenantFacilityId());
        assertThat(storeDetails.getStreet())
            .as("Street code matches")
            .isEqualTo(address.getStreet());
        assertThat(storeDetails.getCountry())
            .as("Country code matches")
            .isEqualTo(address.getCountry());
        assertThat(storeDetails.getCity())
            .as("City code matches")
            .isEqualTo(address.getCity());
        assertThat(storeDetails.getBrand())
            .as("Brand code matches")
            .isEqualTo(address.getCompanyName());
    }

    @Test
    void enrichWithPrice_givenOrderDetails_richOrderDetailsWithTotalPriceIsReturned() {
        // arrange
        String pickJobReference = "19F0DF14-99E8-4EE8-9068-20CC20751935";

        final var itemsPrice = new BigDecimal("12.78");
        final var shipmentCost = new BigDecimal("7.89");

        var orderDetails = OrderDetails.builder()
            .pickJobReference(pickJobReference)
            .itemsPrice(itemsPrice)
            .build();

        when(fulfillmentToolsService.getPickJob(pickJobReference))
            .thenReturn(PickJob.builder()
                .customAttributes(new CustomAttributes(shipmentCost))
                .build());

        // act
        fulfillmentEventEnrichmentService.enrichWithPrice(orderDetails);

        // assert
        assertThat(orderDetails.getShippingPrice())
            .as("Shipping price matches")
            .isEqualTo(shipmentCost);

        assertThat(orderDetails.getItemsPrice())
            .as("Items price matches")
            .isEqualTo(itemsPrice);

        assertThat(orderDetails.getTotalPrice())
            .as("Total price matches")
            .isEqualTo(new BigDecimal("20.67"));

    }

    @Test
    void enrichWithPrice_givenOrderDetailsButNoShippingCostDefined_richOrderDetailsWithTotalPriceIsReturned() {
        // arrange
        String pickJobReference = "FCE38A95-3775-4302-A150-84B722F6E34A";

        final var itemsPrice = new BigDecimal("12.78");

        var orderDetails = OrderDetails.builder()
            .pickJobReference(pickJobReference)
            .itemsPrice(itemsPrice)
            .build();

        when(fulfillmentToolsService.getPickJob(pickJobReference))
            .thenReturn(PickJob.builder()
                .build());

        // act
        assertThrows(
            NullPointerException.class,
            () -> fulfillmentEventEnrichmentService.enrichWithPrice(orderDetails),
            "No Shipment cost was defined");
    }

    @Test
    void enrichWithFacility_givenPickJobPickingFinished_richEventReturned() {
        // arrange
        String facilityRef = "Esb20gpHBL94X5NdMp3C";
        var event = PickJobPickingFinished.builder()
            .facilityRef(facilityRef)
            .build();

        when(fulfillmentToolsService.getFacility(anyString()))
            .thenReturn(facility);

        // act
        EnrichedPickJobPickingFinished richEvent = fulfillmentEventEnrichmentService.enrichWithFacility(event);

        // assert
        assertThat(richEvent.fulfillmentEvent())
            .as("event")
            .isEqualTo(event);
        assertThat(richEvent.facility())
            .as("facility")
            .isEqualTo(facility);

        verify(fulfillmentToolsService).getFacility(facilityRef);
    }
}
