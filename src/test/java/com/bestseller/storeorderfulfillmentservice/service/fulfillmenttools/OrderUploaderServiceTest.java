package com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools;

import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.OrderCreation;
import com.bestseller.storeorderfulfillmentservice.service.S3Service;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderUploaderServiceTest {

    private static final String ORDER_ID = "order-12345";
    private static final String S3_DIRECTORY = "orders/";

    @Mock
    private S3Service s3Service;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private OrderUploaderService orderUploaderService;

    private OrderCreation orderCreation;

    @BeforeEach
    void setUp() {
        orderCreation = OrderCreation.builder()
                .tenantOrderId(ORDER_ID)
                .build();
    }

    @Test
    void uploadOrder_validOrder_shouldUploadToS3WithCorrectFormat() throws IOException {
        // arrange
        byte[] jsonContent = "test content".getBytes();
        when(objectMapper.writeValueAsBytes(orderCreation)).thenReturn(jsonContent);

        // act
        orderUploaderService.uploadOrder(orderCreation);

        // assert
        ArgumentCaptor<String> filenameCaptor = ArgumentCaptor.forClass(String.class);
        verify(s3Service).uploadJsonContent(eq(jsonContent), eq(S3_DIRECTORY), filenameCaptor.capture());

        String filename = filenameCaptor.getValue();
        assertThat(filename)
            .contains(ORDER_ID)
            .endsWith(".json");
    }

    @Test
    void uploadOrder_withJsonProcessingException_shouldLogError() throws IOException {
        // arrange
        when(objectMapper.writeValueAsBytes(any())).thenThrow(JsonProcessingException.class);

        // act
        orderUploaderService.uploadOrder(orderCreation);

        // assert
        verify(s3Service, never()).uploadJsonContent(any(), anyString(), anyString());
    }
}
