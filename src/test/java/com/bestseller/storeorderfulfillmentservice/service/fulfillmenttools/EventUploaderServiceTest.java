package com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.fulfillmenttoolseventsreceived.FulfillmentToolsEventsReceived;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.Payload;
import com.bestseller.storeorderfulfillmentservice.service.S3Service;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EventUploaderServiceTest {

    private static final String EVENT_TYPE = "pick_job_created";
    private static final String S3_DIRECTORY = "fft-events/";

    @Mock
    private S3Service s3Service;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private EventUploaderService eventUploaderService;

    private FulfillmentToolsEventsReceived event;

    @BeforeEach
    void setUp() {
        Payload payload = mock(Payload.class);
        event = new FulfillmentToolsEventsReceived()
                .withEvent(EVENT_TYPE)
                .withPayload(payload);
    }

    @Test
    void uploadEvent_validEvent_shouldUploadToS3WithCorrectFormat() throws IOException {
        // arrange
        byte[] jsonContent = "test content".getBytes();
        when(objectMapper.writeValueAsBytes(event)).thenReturn(jsonContent);

        // act
        eventUploaderService.uploadEvent(event);

        // assert
        ArgumentCaptor<String> filenameCaptor = ArgumentCaptor.forClass(String.class);
        verify(s3Service).uploadJsonContent(eq(jsonContent), eq(S3_DIRECTORY), filenameCaptor.capture());

        String filename = filenameCaptor.getValue();
        assertThat(filename)
            .contains(EVENT_TYPE)
            .endsWith(".json");
    }

    @Test
    void uploadEvent_withJsonProcessingException_shouldLogError() throws IOException {
        // arrange
        when(objectMapper.writeValueAsBytes(any())).thenThrow(JsonProcessingException.class);

        // act
        eventUploaderService.uploadEvent(event);

        // assert
        verify(s3Service, never()).uploadJsonContent(any(), anyString(), anyString());
    }
}
