package com.bestseller.storeorderfulfillmentservice.service;

import com.bestseller.storeorderfulfillmentservice.exception.DocumentGenerationException;
import com.bestseller.storeorderfulfillmentservice.exception.fft.FulfillmentToolsProcessException;
import com.bestseller.storeorderfulfillmentservice.model.order.OrderDetails;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.DocumentService;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.FulfillmentToolsService;
import com.google.common.io.Files;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.doThrow;

// import org.springframework.stereotype.Service;

@ExtendWith(MockitoExtension.class)
@SuppressWarnings({"PMD.AvoidFileStream", "PMD.CloseResource"})
class DocumentServiceTest {

    @InjectMocks
    private DocumentService documentService;

    @Mock
    private FulfillmentToolsService fulfillmentToolsService;

    @Mock
    private FileRenderingService fileRenderingService;

    @Mock
    private S3Service s3Service;

    @Test
    @SuppressWarnings("PMD.WeakVerification") // I couldn't find a way to test the file because it was already deleted on the verification.
    void attachDocuments_fileWasFound_postDocumentToProcessIsCalled() throws IOException {
        // arrange
        var processId = "AB2AD96B-7ABB-46B9-ACD7-DCE8AD385FDE";
        var orderNumber = "TB1267888";
        var orderDetails = OrderDetails
                .builder()
                .processId(processId)
                .orderNumber(orderNumber)
                .build();
        var dummyFile = File.createTempFile("dummy", "pdf");
        String content = "Hello File!";
        Files.write(content.getBytes(), dummyFile);
        ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();

        InputStream invoiceStream = contextClassLoader.getResourceAsStream("dummy.pdf");
        InputStream returnSlipStream = contextClassLoader.getResourceAsStream("dummy.pdf");

        doAnswer(invocation -> {
            OutputStream outputStream = invocation.getArgument(1);
            invoiceStream.transferTo(outputStream);
            return null;
        }).when(fileRenderingService).writeInvoice(eq(orderDetails), any(OutputStream.class));

        doAnswer(invocation -> {
            OutputStream outputStream = invocation.getArgument(1);
            returnSlipStream.transferTo(outputStream);
            return null;
        }).when(fileRenderingService).writeReturnSlip(eq(orderDetails), any(OutputStream.class));

        final int maxExpectedPdfSize = 10_000;
        final byte[] bytes = new byte[maxExpectedPdfSize];
        doAnswer(invocation -> {
            InputStream inputStream = invocation.getArgument(0);
            inputStream.read(bytes);
            return null;
        }).when(fulfillmentToolsService)
                .postDocumentToProcess(any(InputStream.class), eq(processId), eq("invoice-return-label.pdf"));

        // act
        documentService.attachDocuments(orderDetails);

        // assert
        verify(fulfillmentToolsService).postDocumentToProcess(any(InputStream.class), eq(processId), eq("invoice-return-label.pdf"));
        verify(s3Service).uploadDocument(any(ByteArrayInputStream.class), eq("documents/"), eq("TB1267888.pdf"));

        assertThat(bytes).as("Final PDF content")
                .containsSequence("Pages 2".getBytes());
    }

    @Test
    void attachDocuments_fileNotFound_postDocumentToProcessIsNotCalled() {
        // arrange
        var processId = "9F10549C-0B42-483F-8162-642BAA0C63C9";

        var orderDetails = OrderDetails
                .builder()
                .processId(processId)
                .orderNumber("TB1221")
                .build();
        doNothing().when(fileRenderingService).writeInvoice(any(), any());
        doNothing().when(fileRenderingService).writeReturnSlip(any(), any());

        // act
        FulfillmentToolsProcessException exception = Assertions.assertThrows(
                FulfillmentToolsProcessException.class,
                () -> documentService.attachDocuments(orderDetails));

        // assert
        assertThat(exception.getMessage())
                .as("Message contains main exception message")
                .contains("Failed to sent PDF to order TB1221 - process %s".formatted(processId));
    }

    @Test
    void generateDocument_shouldThrowDocumentGenerationException_whenUnexpectedExceptionOccurs() throws IOException {
        // Arrange
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OrderDetails orderDetails = new OrderDetails();
        orderDetails.setOrderNumber("12345");

        doThrow(new NullPointerException("Test NPE"))
            .when(fileRenderingService)
            .writeInvoice(eq(orderDetails), any(ByteArrayOutputStream.class));

        // Act & Assert
        DocumentGenerationException ex = assertThrows(DocumentGenerationException.class, () ->
            documentService.generateDocument(orderDetails, outputStream)
        );

        assertThat(ex.getMessage())
            .as("Exception message contains order number")
            .contains("Unexpected error during document generation for orderNumber= 12345");
    }
}
