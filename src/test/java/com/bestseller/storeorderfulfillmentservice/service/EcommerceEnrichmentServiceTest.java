package com.bestseller.storeorderfulfillmentservice.service;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.ecom.EnrichedOrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.pcs.ProductInformation;
import com.bestseller.storeorderfulfillmentservice.service.pcs.ProductInformationService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import util.OrderPartsCreatedGenerator;

import java.util.Map;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static util.OrderPartsCreatedGenerator.EAN_1;
import static util.OrderPartsCreatedGenerator.EAN_2;

@ExtendWith(MockitoExtension.class)
class EcommerceEnrichmentServiceTest {

    @InjectMocks
    private EcommerceEnrichmentService ecommerceEnrichmentService;

    @Mock
    private ProductInformationService productInformationService;

    @Test
    void enrichOrderWithProductInformation_givenValidObject_correctEnrichedObjectIsReturned() {
        // arrange
        OrderPartsCreated orderPartsCreated = OrderPartsCreatedGenerator.generate();
        Map<String, ProductInformation> productsInformation = Map.of(
                EAN_1, ProductInformation.builder().ean(EAN_1).build(),
                EAN_2, ProductInformation.builder().ean(EAN_2).build()
        );
        when(productInformationService.getProductInformation(Set.of(EAN_1, EAN_2)))
                .thenReturn(productsInformation);

        // act
        EnrichedOrderPartsCreated enriched = ecommerceEnrichmentService.enrichOrderWithProductInformation(orderPartsCreated);

        // assert
        assertThat(enriched.getOrderPartsCreated()).as("Order parts created")
                .isEqualTo(orderPartsCreated);
        assertThat(enriched.getProductsInformation()).as("Order parts created")
                .isEqualTo(productsInformation);

    }

}
