package util;

import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.ArticleAttribute;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.ConsumerAddress;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.PackJobCreated;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.PackLineItem;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.PackLineItemArticle;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * PackJobCreated Fulfillment Tools event generator.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PackJobCreatedPayloadGenerator {

    //payload
    public static final Instant CREATED = Instant.parse("2023-02-03T09:45:51.525Z");
    public static final Instant LAST_MODIFIED = Instant.parse("2023-02-03T09:45:51.525Z");
    public static final Instant ORDER_DATE = Instant.parse("2023-02-01T09:45:51.525Z");
    public static final String PINK_BLUE_DENIM_STYLE_OPTION = "12206084_PinkBlueDenim";

    public static final String PICK_JOB_REF = "CAA194ED-F833-40E3-9E6F-90AB23FEDAA8";

    public static final String CITY = "Haarlem";
    public static final String FIRST_NAME = "James";
    public static final String LAST_NAME = "Cameron";
    public static final String HOUSE_NUMBER = "78D";
    public static final String COUNTRY = "DE";
    public static final String POSTAL_CODE = "1287";
    public static final String PANDORA_STRAAT = "PandoraStraat";
    public static final String BLUE_DENIM_STYLE_OPTION = "12206084_BlueDenim";
    public static final String BLUE_JEANS_TITLE = "Blue Jeans";
    public static final String EAN_1 = "1234567891215";
    public static final String PRICE_EAN_1 = "46.98";

    public static final String DISCOUNT_VALUE_EAN_1 = "12.39";

    public static final String PINK_BLUE_JEANS_TITLE = "Pink Blue Jeans";
    public static final String EAN_2 = "1234567891219";
    public static final String PRICE_EAN_2 = "33.99";
    public static final int QUANTITY_EAN_1 = 4;
    public static final int QUANTITY_EAN_2 = 2;

    public static final String PROCESS_ID = "33BD62C8-862F-45E5-A949-E1C5F5D92691";

    public static final String FACILITY_REF = "FD0DEF61-DF79-4DAE-927A-8C35092CCADF";

    // article
    private static final String ORDER_ID = "D3A9AF8E-57AF-4691-A891-3D0829101AA7";

    private static final String TENANT_ORDER_ID = "OL122178";

    /**
     * Creates a new PackJobCreated.
     *
     * @return new pack job created payload.
     */
    public static PackJobCreated generate() {
        return PackJobCreated.builder()
            .created(CREATED)
            .lastModified(LAST_MODIFIED)
            .processId(PROCESS_ID)
            .orderId(ORDER_ID)
            .facilityRef(FACILITY_REF)
            .tenantOrderId(TENANT_ORDER_ID)
            .orderDate(ORDER_DATE)
            .pickJobRef(PICK_JOB_REF)
            .recipient(ConsumerAddress.builder()
                .city(CITY)
                .firstName(FIRST_NAME)
                .lastName(LAST_NAME)
                .houseNumber(HOUSE_NUMBER)
                .country(COUNTRY)
                .postalCode(POSTAL_CODE)
                .street(PANDORA_STRAAT)
                .build())
            .lineItems(List.of(
                PackLineItem
                    .builder()
                    .quantity(QUANTITY_EAN_1)
                    .article(PackLineItemArticle
                        .builder()
                        .ean(EAN_1)
                        .title(BLUE_JEANS_TITLE)
                        .attributes(List.of(ArticleAttribute
                                .builder()
                                .key("STYLE_OPTION")
                                .value(BLUE_DENIM_STYLE_OPTION)
                                .build(),
                            ArticleAttribute
                                .builder()
                                .key("PRICE")
                                .value(PRICE_EAN_1)
                                .build(),
                            new ArticleAttribute("SIZE", "XXXXXXXL"),
                            ArticleAttribute
                                .builder()
                                .key("DISCOUNT_VALUE")
                                .value(DISCOUNT_VALUE_EAN_1)
                                .build()))
                        .build())
                    .build(),
                PackLineItem
                    .builder()
                    .quantity(QUANTITY_EAN_2)
                    .article(PackLineItemArticle
                        .builder()
                        .ean(EAN_2)
                        .title(PINK_BLUE_JEANS_TITLE)
                        .attributes(List.of(ArticleAttribute
                                .builder()
                                .key("STYLE_OPTION")
                                .value(PINK_BLUE_DENIM_STYLE_OPTION)
                                .build(),
                            ArticleAttribute
                                .builder()
                                .key("PRICE")
                                .value(PRICE_EAN_2)
                                .build(),
                            new ArticleAttribute("SIZE", "XXXXXXXL")))
                        .build())
                    .build()
            ))
            .build();
    }
}
