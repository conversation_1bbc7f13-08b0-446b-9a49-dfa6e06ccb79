package util;

import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.Article;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.LineItem;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobAborted;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * PickJobAborted Generator.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PickJobAbortedPayloadGenerator {

    public static final String EAN_1 = "1234567891215";
    public static final String EAN_2 = "1554567891215";

    public static final Instant EVENT_DATE = Instant.parse("2023-02-01T09:45:51.525Z");

    public static final int QUANTITY_EAN_1 = 4;
    public static final int QUANTITY_EAN_2 = 2;

    public static final String ORDER_ID = "aaaaaaaa-hhhh-mmmm-eeee-ttttttttttttt";

    /**
     * Generate PickJobAborted.
     *
     * @return
     */
    public static PickJobAborted.PickJobAbortedBuilder<?, ?> generate() {
        return PickJobAborted.builder()
            .pickLineItems(List.of(LineItem
                    .builder()
                    .quantity(QUANTITY_EAN_1)
                    .article(new Article(EAN_1))
                    .build(),
                LineItem
                    .builder()
                    .quantity(QUANTITY_EAN_2)
                    .article(new Article(EAN_2))
                    .build())
            )
            .created(EVENT_DATE)
            .orderId(ORDER_ID);
    }
}
