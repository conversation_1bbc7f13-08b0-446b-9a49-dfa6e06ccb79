package util;

import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.Article;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.LineItem;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobPickingFinished;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * PickJobCreated Fulfillment Tools event generator.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PickJobPickingFinishedPayloadGenerator {

    //payload
    public static final Instant CREATED = Instant.parse("2023-02-03T09:45:51.525Z");
    public static final Instant LAST_MODIFIED = Instant.parse("2023-02-03T09:45:51.525Z");
    public static final Instant ORDER_DATE = Instant.parse("2023-02-01T09:45:51.525Z");
    public static final String TENANT_ID = "TENANT_ID";

    //article
    public static final String ARTICLE_ID = "ARTICLE_ID";

    /**
     * Creates a new PickJobPickingFinished.
     *
     * @return new pick job picking finished payload.
     */
    public static PickJobPickingFinished generate() {
        return PickJobPickingFinished.builder()
            .created(CREATED)
            .lastModified(LAST_MODIFIED)
            .pickLineItems(List.of(generatePickLineItem()))
            .orderDate(ORDER_DATE)
            .tenantOrderId(TENANT_ID)
            .build();
    }

    /**
     * Creates a new PickLineItem.
     *
     * @return new pick line item.
     */
    public static LineItem generatePickLineItem() {
        return LineItem.builder()
            .article(generateArticle())
            .quantity(1)
            .build();
    }

    /**
     * Creates a new Article.
     *
     * @return new Article.
     */
    public static Article generateArticle() {
        return new Article(ARTICLE_ID);
    }
}
