package util;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.ShippingInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPart;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.pcs.ProductInformation;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;


/**
 * OrderPartsCreated event generator.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OrderPartsCreatedGenerator {

    public static final int QUANTITY_1 = 1;
    public static final String EAN_1 = "1234567891245";
    public static final int QUANTITY_2 = 2;
    public static final String EAN_2 = "1234567891249";
    public static final String FULFILLMENT_NODE = "SHIP_FROM_STORE_DE";
    public static final String ORDER_ID = "9912278777";
    public static final String PLACED_DATE = "2023-03-22T11:52:17.307Z";
    public static final BigDecimal RETAIL_PRICE_2 = new BigDecimal("19.0");
    public static final BigDecimal RETAIL_PRICE_1 = new BigDecimal("13.0");
    public static final BigDecimal DISCOUNT_VALUE_1 = new BigDecimal("1.99");
    public static final String ADDRESS = "address";

    public static final String PICK_UP_POINT_CODE = "1234";
    public static final String ZIPCODE = "2020 KO";
    public static final String HOUSE_NUMBER = "90";
    public static final String CITY = "Starling City";

    public static final String COMPANY_NAME = "Bestseller";
    public static final String LAST_NAME = "Queen";
    public static final String PHONE_NUMBER = "+31678987767";
    public static final String COUNTRY = "NL";
    public static final String FIRST_NAME = "Oliver";
    public static final String EMAIL = "<EMAIL>";
    public static final String PRODUCT_NAME_1 = "JJ Jeans";
    public static final String PRODUCT_NAME_2 = "Scarf";
    public static final BigDecimal SHIPPING_FEES = new BigDecimal("4.80");
    public static final String STYLE_NUMBER_EAN_1 = "12218990";
    public static final String STYLE_OPTION_EAN_1 = "12218990_BLUE";

    public static final String EDI_STYLE_NAME_EAN_1 = "Edi Style Name Ean 1";
    public static final String IMAGE_EAN_1 = "http://test.bestseller.com/image/ean1";

    public static final String LENGTH_EAN_1 = "10";
    public static final String SIZE_EAN_1 = "S/M";
    public static final String COLOR_EAN_1 = "Blue";

    public static final String STYLE_NUMBER_EAN_2 = "12218991";
    public static final String STYLE_OPTION_EAN_2 = "12218991_PINK";
    public static final String EDI_STYLE_NAME_EAN_2 = "Edi Style Name Ean 2";
    public static final String IMAGE_EAN_2 = "http://test.bestseller.com/image/ean2";
    public static final String LENGTH_EAN_2 = null;
    public static final String SIZE_EAN_2 = "13";
    public static final String COLOR_EAN_2 = "Pink";
    public static final String BRAND_JJ = "JJ";
    public static final String BRAND_VM = "VM";
    public static final String BRAND_NAME_JJ = "Jack & Jones";
    public static final String BRAND_NAME_VM = "Vero Moda";

    public static final String CARRIER = "POSTNL";
    public static final String BILLING_ADDRESS_1 = "billingAddress1";
    public static final String BILLING_ADDRESS_2 = "billingAddress2";
    public static final String BILLING_ADDRESS_3 = "billingAddress3";
    public static final String BILLING_ZIPCODE = "1011 IO";
    public static final String BILLING_HOUSE_NUMER = "60";
    public static final String BILLING_CITY = "BillingCity";
    public static final String BILLING_LAST_NAME = "Billing Last Name";
    public static final String BILLING_FIRST_NAME = "Billing First Name";

    public static final String CARRIER_CUSTOMER_NUMBER_LITERAL = "carrierCustomerNumber";
    public static final String CARRIER_CUSTOMER_NUMBER = "123456789";

    public static OrderPartsCreated generate() {
        return generate(COUNTRY);
    }

    /**
     * Generate a OrderPartsCreated.
     *
     * @return orderPartsCreated
     */
    public static OrderPartsCreated generate(String country) {

        Address billingAddress = new Address()
            .withAddressLine1(ADDRESS)
            .withAddressLine3(PICK_UP_POINT_CODE)
            .withZipcode(ZIPCODE)
            .withHouseNumber(HOUSE_NUMBER)
            .withCity(CITY)
            .withLastName(LAST_NAME)
            .withPhoneNumber(PHONE_NUMBER)
            .withCountry(country)
            .withFirstName(FIRST_NAME);
        return new OrderPartsCreated()
            .withPlacedDate(ZonedDateTime.parse(PLACED_DATE))
            .withOrderId(ORDER_ID)
            .withFulfillmentNode(FULFILLMENT_NODE)
            .withChannel("Channel")
            .withBrand(BRAND_JJ)
            .withOrderDetails(
                new OrderDetails()
                    .withCarrierVariant("HOME")
                    .withShippingFees(SHIPPING_FEES)
                    .withCarrier(CARRIER)
                    .withShippingMethod("STANDARD")
            )
            .withCustomerInformation(new CustomerInformation()
                .withBillingAddress(billingAddress)
                .withEmail(EMAIL)
                .withCustomerLocale(COUNTRY))
            .withShippingInformation(new ShippingInformation()
                .withShippingAddress(billingAddress).withParcelLocker(COMPANY_NAME))
            .withOrderParts(List.of(
                new OrderPart()
                    .withOrderPartNumber(1)
                    .withOrderLines(
                        List.of(new OrderLine()
                                .withProductName(PRODUCT_NAME_1)
                                .withQuantity(QUANTITY_1)
                                .withDiscountValue(DISCOUNT_VALUE_1)
                                .withEan(EAN_1)
                                .withLineNumber(1)
                                .withRetailPrice(RETAIL_PRICE_1)
                                .withTaxPercentage(new BigDecimal("0.0")),
                            new OrderLine()
                                .withProductName(PRODUCT_NAME_2)
                                .withQuantity(QUANTITY_2)
                                .withDiscountValue(null)
                                .withEan(EAN_2)
                                .withLineNumber(2)
                                .withRetailPrice(RETAIL_PRICE_2)
                                .withTaxPercentage(new BigDecimal("0.0")))
                    )));
    }

    /**
     * Generate a OrderPartsCreated with 2 different addresses.
     *
     * @return orderPartsCreated
     */
    public static OrderPartsCreated generate2DifferentAddresses(String country) {

        OrderPartsCreated orderPartsCreated = generate(country);

        Address billingAddress = new Address()
            .withAddressLine1(BILLING_ADDRESS_1)
            .withAddressLine2(BILLING_ADDRESS_2)
            .withAddressLine3(BILLING_ADDRESS_3)
            .withZipcode(BILLING_ZIPCODE)
            .withHouseNumber(BILLING_HOUSE_NUMER)
            .withCity(BILLING_CITY)
            .withLastName(BILLING_LAST_NAME)
            .withCountry(country)
            .withFirstName(BILLING_FIRST_NAME);
        orderPartsCreated.getCustomerInformation().setBillingAddress(billingAddress);

        return orderPartsCreated;
    }

    /**
     * Generate the product information related to this Order parts.
     *
     * @return products information.
     */
    public static Map<String, ProductInformation> generateProductInformation() {
        return Map.of(EAN_1, ProductInformation.builder()
                .color(COLOR_EAN_1)
                .ean(EAN_1)
                .ediStyleName(EDI_STYLE_NAME_EAN_1)
                .imageReferencesUrls(List.of(IMAGE_EAN_1))
                .productName(PRODUCT_NAME_1)
                .brandAbbreviation(BRAND_JJ)
                .brand(BRAND_NAME_JJ)
                .length(LENGTH_EAN_1)
                .size(SIZE_EAN_1)
                .styleNumber(STYLE_NUMBER_EAN_1)
                .styleOption(STYLE_OPTION_EAN_1)
                .build(),
            EAN_2, ProductInformation.builder()
                .color(COLOR_EAN_2)
                .ean(EAN_2)
                .ediStyleName(EDI_STYLE_NAME_EAN_2)
                .imageReferencesUrls(List.of(IMAGE_EAN_2))
                .productName(PRODUCT_NAME_2)
                .brandAbbreviation(BRAND_VM)
                .brand(BRAND_NAME_VM)
                .length(LENGTH_EAN_2)
                .size(SIZE_EAN_2)
                .styleNumber(STYLE_NUMBER_EAN_2)
                .styleOption(STYLE_OPTION_EAN_2)
                .build());
    }

    /**
     * Generate the product information related to this Order parts without image.
     *
     * @return products information.
     */
    public static Map<String, ProductInformation> generateProductInformationWithoutImage() {
        return Map.of(EAN_1, ProductInformation.builder()
                .color(COLOR_EAN_1)
                .ean(EAN_1)
                .ediStyleName(EDI_STYLE_NAME_EAN_1)
                .imageReferencesUrls(List.of())
                .productName(PRODUCT_NAME_1)
                .length(LENGTH_EAN_1)
                .size(SIZE_EAN_1)
                .styleNumber(STYLE_NUMBER_EAN_1)
                .styleOption(STYLE_OPTION_EAN_1)
                .build(),
            EAN_2, ProductInformation.builder()
                .color(COLOR_EAN_2)
                .ean(EAN_2)
                .ediStyleName(EDI_STYLE_NAME_EAN_2)
                .imageReferencesUrls(null)
                .productName(PRODUCT_NAME_2)
                .length(LENGTH_EAN_2)
                .size(SIZE_EAN_2)
                .styleNumber(STYLE_NUMBER_EAN_2)
                .styleOption(STYLE_OPTION_EAN_2)
                .build());
    }
}
