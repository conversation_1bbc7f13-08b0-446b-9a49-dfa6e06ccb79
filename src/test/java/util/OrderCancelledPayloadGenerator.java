package util;

import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.Article;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.ConsumerInformation;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.LineItem;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.OrderCancelled;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.ConsumerAddress;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * PickJobAborted Generator.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OrderCancelledPayloadGenerator {

    public static final String EAN_1 = "1234567891215";
    public static final String EAN_2 = "1554567891215";

    public static final Instant EVENT_DATE = Instant.parse("2023-02-01T09:45:51.525Z");

    public static final int QUANTITY_EAN_1 = 4;
    public static final int QUANTITY_EAN_2 = 2;

    public static final String ORDER_ID = "b155e180-47dc-11ee-be56-0242ac120002";
    public static final String COUNTRY = "NL";

    /**
     * Generate PickJobAborted.
     *
     * @return
     */
    public static OrderCancelled.OrderCancelledBuilder<?, ?> generate() {
        return OrderCancelled.builder()
            .consumer(ConsumerInformation.builder()
                .addresses(List.of(ConsumerAddress.builder()
                    .country(COUNTRY)
                    .build()))
                .build())
            .orderLineItems(List.of(LineItem
                    .builder()
                    .quantity(QUANTITY_EAN_1)
                    .article(new Article(EAN_1))
                    .build(),
                LineItem
                    .builder()
                    .quantity(QUANTITY_EAN_2)
                    .article(new Article(EAN_2))
                    .build())
            )
            .created(EVENT_DATE)
            .tenantOrderId(ORDER_ID);
    }
}
