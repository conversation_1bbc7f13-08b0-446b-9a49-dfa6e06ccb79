package util;

import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.Article;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.LineItem;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.notroutable.RoutingPlanNotRoutable;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * RoutingPlanNotRoutable Fulfillment Tools event generator.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RoutingPlanNotRoutablePayloadGenerator {

    //payload
    public static final Instant CREATED = Instant.parse("2023-02-03T09:45:51.525Z");
    public static final Instant LAST_MODIFIED = Instant.parse("2023-02-03T09:45:51.525Z");
    public static final Instant ORDER_DATE = Instant.parse("2023-02-01T09:45:51.525Z");
    public static final String TENANT_ID = "TENANT_ID";

    //article
    public static final String ARTICLE_ID = "ARTICLE_ID";

    /**
     * Creates a new RoutingPlanNotRoutable with two order lines.
     *
     * @return new routing plan not routable event.
     */
    public static RoutingPlanNotRoutable.RoutingPlanNotRoutableBuilder<?, ?> generate() {
        return RoutingPlanNotRoutable.builder()
            .created(CREATED)
            .lastModified(LAST_MODIFIED)
            .orderLineItems(List.of(generateOrderLineItems(), generateOrderLineItems()))
            .orderDate(ORDER_DATE)
            .tenantOrderId(TENANT_ID);
    }

    /**
     * Creates a new OrderLineItem.
     *
     * @return new order line item.
     */
    public static LineItem generateOrderLineItems() {
        return LineItem.builder()
            .article(generateArticle())
            .quantity(1)
            .build();
    }

    /**
     * Creates a new Article.
     *
     * @return new Article.
     */
    public static Article generateArticle() {
        return new Article(ARTICLE_ID);
    }
}
