package util;

import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.dispatched.HandoverJobHandedOver;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * HandoverJobHandedOver Fulfillment Tools event generator.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class HandoverJobHandedOverPayloadGenerator {

    //payload
    public static final Instant CREATED = Instant.parse("2023-02-03T09:45:51.525Z");
    public static final Instant LAST_MODIFIED = Instant.parse("2023-02-03T09:45:51.525Z");
    public static final Instant ORDER_DATE = Instant.parse("2023-02-01T09:45:51.525Z");
    public static final String CARRIER_REF = "4F1005E3-64D5-4A40-8817-081557185909";

    public static final String CARRIER_TRACKING_NUMBER = "045BBBB";
    public static final String FACILITY_REF = "FACILITY_ID";

    //article
    public static final String TENANT_ID = "TENANT_ID";
    public static final String PICK_JOB_REFERENCE = "PICK_JOB_REFERENCE";

    /**
     * Creates a new HandoverJobHandedOver.
     *
     * @return new HandoverJobHandedOver.
     */
    public static HandoverJobHandedOver generate() {
        return HandoverJobHandedOver.builder()
            .created(CREATED)
            .lastModified(LAST_MODIFIED)
            .carrierRef(CARRIER_REF)
            .carrierTrackingNumber(CARRIER_TRACKING_NUMBER)
            .facilityRef(FACILITY_REF)
            .orderDate(ORDER_DATE)
            .tenantOrderId(TENANT_ID)
            .pickJobRef(PICK_JOB_REFERENCE)
            .build();
    }
}
