package util;

import com.bestseller.storeorderfulfillmentservice.model.pcs.ProductInformation;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Generates ProductInformation list message for test purposes.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ProductInformationGenerator {
    public static final String EDI_STYLE_NAME = "BASIC O-NECK TEE S/S NOOS";
    public static final String SIZE = "M";

    /**
     * Creates a new product information item.
     *
     * @param ean ean
     * @return product information with given ean
     */
    public static ProductInformation createEan(String ean) {
        return ProductInformation.builder()
                .ean(ean)
                .ediStyleName(EDI_STYLE_NAME)
                .size(SIZE)
                .build();
    }
}
