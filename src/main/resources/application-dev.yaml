logging:
  level:
    web: trace

# Credentials
admin:
  username: admin
  password: "{noop}admin"

# Kafka
kafka:
  broker:
    list: localhost

# Fulfillment tools
fft:
  endpoint: http://${wiremock.host}:${wiremock.port}/
  auth:
    key: fftAuth<PERSON>ey
    email: <EMAIL>
    password: fftAuthPassword
    endpoint: http://${wiremock.host}:${wiremock.port}/v1/
  webclient:
    backoff:
      delay: PT0.5s

wiremock:
  port: 9999
  host: localhost

# S3
s3:
  bucket: bucket
aws:
  local:
    s3:
      endpoint: http://localhost:4566

# PCS
pcs:
  endpoint: http://${wiremock.host}:${wiremock.port}/graphql