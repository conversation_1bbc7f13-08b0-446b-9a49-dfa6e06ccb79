info:
  app:
    name: Store Order Fulfillment Service
    version: ${DD_VERSION:local}

server:
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: on_param
  tomcat:
    mbeanregistry:
      enabled: true

management:
  server:
    base-path: /actuator
  endpoints:
    web:
      exposure:
        include: health,info,metrics,bindings,mappings,loggers
  endpoint:
    health:
      show-details: WHEN_AUTHORIZED
  info:
    env:
      enabled: true
    java:
      enabled: true
    build:
      enabled: true
  metrics:
    export:
      datadog:
        api-key: ${DD_API_KEY}
        application-key: ${DD_APPLICATION_KEY}
        enabled: ${DD_ENABLED:false}
    tags:
      service: sofs
      env: ${spring.profiles.active}

kafka:
  topic:
    prefix: ''

spring:
  cloud:
    stream:
      kafka:
        binder:
          brokers: ${kafka.broker.list}
          configuration:
            security.protocol: PLAINTEXT
      default:
        contentType: application/json
        group: StoreOrderFulfillmentService
        producer:
          header-mode: none
        kafka:
          default:
            consumer:
              start-offset: latest
        bindings:
          default:
            content-type: application/json
      bindings:
        transformFulfillmentToolsEvent-in-0:
          destination: ${kafka.topic.prefix}FulfillmentToolsEventsReceived
        transformFulfillmentToolsEvent-out-0:
          destination: ${kafka.topic.prefix}OrderLineAcknowledged
        transformFulfillmentToolsEvent-out-1:
          destination: ${kafka.topic.prefix}OrderLineDispatched
        transformFulfillmentToolsEvent-out-2:
          destination: ${kafka.topic.prefix}OrderPartRejected
        transformFulfillmentToolsEvent-out-3:
          destination: ${kafka.topic.prefix}OrderPartsCancelled
        exportOrderToFulfillmentTools-in-0:
          destination: ${kafka.topic.prefix}OrderPartsCreated
        exportOrderToFulfillmentTools-out-0:
          destination: ${kafka.topic.prefix}OrderLineExported
        exportOrderToFulfillmentTools-out-1:
          destination: ${kafka.topic.prefix}OrderPartRejected
    function:
      definition: transformFulfillmentToolsEvent;exportOrderToFulfillmentTools

springdoc:
  swagger-ui:
    path: /

order:
  reroute:
    period: P0d

fft:
  endpoint: ${FFT_ENDPOINT}
  auth:
    key: ${FFT_AUTH_KEY}
    email: ${FFT_AUTH_EMAIL}
    password: ${FFT_AUTH_PASSWORD}
    endpoint: ${FFT_AUTH_ENDPOINT}
  webclient:
    backoff:
      delay: PT2s

pcs:
  endpoint: ${PCS_ENDPOINT}