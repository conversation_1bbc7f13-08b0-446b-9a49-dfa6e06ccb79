<html>
<head>
    <#import "common.ftlh" as common>
    <@common.style />
    <meta content="text/html; charset=UTF-8" http-equiv="content-type"/>
</head>
<body>
<div class="center-div">
    <div class="two-col-container">
        <div class="left">
            <h1 class="uppercase">${localized.invoice}</h1>
        </div>
        <div class="right">
            <@common.logo />
        </div>
    </div>

    <@common.header />
    <div class="order-body">
        <h3 class="uppercase">${localized.itemsOverview}</h3>
        <p>${localized.deliveryContainsFollowingItems}:</p>
        <table>
            <tr>
                <th class="uppercase" style="width: 50%">
                    ${localized.item}
                </th>
                <th class="uppercase" style="width: 20%">
                    ${localized.itemNumber}
                </th>
                <th class="uppercase" style="width: 20%">
                    ${localized.itemPrice}
                </th>
                <th class="uppercase" style="width: 20%">
                    ${localized.itemQuantity}
                </th>
            </tr>
            <#list data.orderLines as orderline>
                <tr>
                    <td>${orderline.description()}, ${orderline.size()}</td>
                    <td>${orderline.ean()}</td>
                    <td>${orderline.price()}</td>
                    <td>${orderline.quantity()}</td>
                </tr>
            </#list>
        </table>

        <hr style="margin-top: 0; margin-left: 0.5em; margin-right: 0.5em"/>
        <div class="right">
            ${localized.totalPrice}: ${data.itemsPrice} ${localized.currencySign}<br/>
            ${localized.shippingCost}: ${data.shippingPrice} ${localized.currencySign}<br/>
            ${localized.totalAmount}: ${data.totalPrice} ${localized.currencySign}<br/>
            <span style="font-size: 8pt;color: gray">${localized.pricesIncludeVat}</span>
        </div>
    </div>
    <div class="footer">
        <p>${localized.thanksForThePurchase?replace("{BRAND_NAME}", brand.name)}</p>
        <p>${localized.returnConditions}:</p>
        <ol>
            <#list localized.returnConditionsItems as returnConditionsItem>
                <li>${returnConditionsItem?replace("{BRAND_WEBSITE}", brand.website)}</li>
            </#list>
        </ol>
    </div>
    <hr style="margin-top: 4em"/>
    <div class="footer-address center">
        ${localized.company.name} <br/>
        <#if localized.company.complement??>
            ${localized.company.complement} <br/>
        </#if>
        ${brand.website}
    </div>
</div>
</body>
</html>