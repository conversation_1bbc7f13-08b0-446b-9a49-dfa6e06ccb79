<#macro style>
    <style>
        @font-face {
            font-family: "Noto Serif";
            src: url(template/fonts/NotoSerif-Regular.ttf) format("truetype");
        }

        @page {
            margin: 5%;
            size: A4 portrait;
            @bottom-center {
                content: counter(page);
            }
        }

        table {
            width: 100%
        }

        body {
            font-family: 'Noto Serif', serif;
            font-size: 10pt;
        }

        .center-div {
            margin-left: auto;
            margin-right: auto;
            text-align: left;
        }

        .header-address {
            display: block;
            line-height: 0.5em;
            color: #999;
        }

        h1 {
            font-size: 12pt;
            font-weight: normal;
            text-transform: uppercase;
        }

        h3 {
            font-weight: lighter;
            text-transform: uppercase;
        }

        .left {
            position: absolute;
            text-align: left;
            left: 1%;
        }

        .right {
            text-align: right;
            right: 0;
        }

        .center {
            text-align: center;
            margin: auto;
        }

        hr {
            color: gray;
            margin-top: 2em;
        }

        .bold {
            font-weight: bold;
        }

        th {
            text-align: left;
            background-color: #999;
            color: white;
            text-transform: uppercase;
            padding-top: 1em;
            padding-bottom: 1em;
            padding-left: 0.5em;
        }

        td {
            font-weight: lighter;
            padding: 0.5em;
        }

        .footer-address {
            margin-top: 2em;
        }

        .barcode {
            padding: 0;
            margin-top: 1em;
            margin-right: -2.5em;
            right: 0;
        }

        .uppercase {
            text-transform: uppercase;
        }
    </style>
</#macro>

<#macro header>
    <div class="header-address">
        <p>Store | ${data.storeDetails.getLocationChainCode()} ${data.storeDetails.brand}
            (${data.storeDetails.city})</p>
        <p> ${data.storeDetails.street} ${data.storeDetails.city}</p>
    </div>

    <div class="two-col-container">
        <div class="left">
            <h3 class="uppercase">${localized.shippingAddress}</h3>
            ${data.customerDetails.firstName()} ${data.customerDetails.lastname()}<br/>
            ${data.customerDetails.street()} ${data.customerDetails.houseNumber()}
            ${data.customerDetails.city()} ${data.customerDetails.postalCode()} <br/>
            ${data.customerDetails.country()}
        </div>
        <div class="right">
            <h3 class="uppercase">${localized.orderNumber}: <i>${data.orderNumber}</i></h3>
            <span>${localized.orderDate} : <span class="bold">${data.placementDate}</span></span><br/>
            <span>${localized.shippingDate} : <span class="bold">${data.shipmentDate}</span></span><br/>
            <img style="width: auto" class="barcode" src="${barcodeImage}" alt="Barcode Image" height="50px"/>
        </div>
    </div>
    <hr/>
</#macro>

<#macro logo>
    <img style="height: auto" src="${brand.logoUrl}" alt="Brand Logo" width="250"/>
</#macro>