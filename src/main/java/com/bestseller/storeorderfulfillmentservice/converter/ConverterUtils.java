package com.bestseller.storeorderfulfillmentservice.converter;

import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.Address;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.facility.Facility;
import lombok.experimental.UtilityClass;

/**
 * Utility class for converting between different types.
 */
@UtilityClass
public final class ConverterUtils {

    public int getLocationChainCode(Facility facility) {
        return Integer.parseInt(facility.tenantFacilityId());
    }

    public String toWarehouse(Facility facility) {
        return toWarehouse(facility.address());
    }

    public String toWarehouse(Address address) {
        return toWarehouse(address.getCountry());
    }

    public String toWarehouse(String country) {
        return "SHIP_FROM_STORE_%s".formatted(country);
    }
}
