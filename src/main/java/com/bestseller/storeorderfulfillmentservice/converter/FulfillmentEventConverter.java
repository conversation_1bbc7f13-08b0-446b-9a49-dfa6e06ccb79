package com.bestseller.storeorderfulfillmentservice.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.LineItem;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.OrderCancelled;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.dispatched.HandoverJobHandedOver;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.notroutable.RoutingPlanNotRoutable;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobCreated;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedHandoverJobHandedOver;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedPickJobAborted;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedPickJobCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.Parcel;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.carrier.Carrier;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.facility.Facility;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.picked.PickJob;
import com.bestseller.storeorderfulfillmentservice.model.pcs.EnrichedPickJobPickingFinished;
import com.bestseller.storeorderfulfillmentservice.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.CancellationReason.ITEM_NOT_AVAILABLE;

/**
 * Converts FulfillmentTools events to BESTSELLER e-commerce OMS events.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class FulfillmentEventConverter {

    public static final String BRING = "BRING";
    private final OrderPartsRejectedConverter orderPartsRejectedConverter;

    private final DateUtils dateUtils;

    /**
     * Converts PICK_JOB_CREATED.
     *
     * @param richPickJobCreated fulfillment event enriched with extra information
     * @return one OMS event per order line
     */
    public List<OrderLineAcknowledged> convert(EnrichedPickJobCreated richPickJobCreated) {
        PickJobCreated source = richPickJobCreated.fulfillmentEvent();
        Facility facility = richPickJobCreated.facility();

        ZonedDateTime createdDate = dateUtils.asZonedDateTime(source.getCreated());
        int locationChainCode = ConverterUtils.getLocationChainCode(facility);
        String warehouse = ConverterUtils.toWarehouse(facility);

        return source.getPickLineItems().stream()
            .map(pickLineItem -> new OrderLineAcknowledged()
                .withAcknowledgementDate(createdDate)
                .withOrderId(source.getTenantOrderId())
                .withEan(pickLineItem.getArticle().getEan())
                .withQuantity(pickLineItem.getQuantity())
                .withWarehouse(warehouse)
                .withStoreId(locationChainCode))
            .collect(Collectors.toList());
    }

    /**
     * Converts HANDOVERJOB_HANDED_OVER.
     *
     * @param richFulfillmentEvent fulfillment event enriched with extra information
     * @return one OMS event per order line
     */
    public List<OrderLineDispatched> convert(EnrichedHandoverJobHandedOver richFulfillmentEvent) {
        HandoverJobHandedOver source = richFulfillmentEvent.fulfillmentEvent();
        Facility facility = richFulfillmentEvent.facility();
        Parcel parcel = richFulfillmentEvent.parcel();
        PickJob pickJob = richFulfillmentEvent.pickJob();
        Carrier carrier = richFulfillmentEvent.carrier();

        ZonedDateTime dispatchDate = dateUtils.asZonedDateTime(source.getCreated());
        String warehouse = ConverterUtils.toWarehouse(facility);
        int locationChainCode = ConverterUtils.getLocationChainCode(facility);
        String returnShipmentId = parcel.parcel().returnLabelId();
        String carrierTrackingNumber = carrierIsBring(carrier)
            ? source.getHandoverJobParcelInfo().getCarrierParcelRef()
            : source.getCarrierTrackingNumber();


        if (!StringUtils.hasLength(carrierTrackingNumber)) {
            log.error("Order Line Dispatched {} doesn't have trackingNumber. This event will be produced but not processed by OMS.",
                source.getTenantOrderId());
        }

        return pickJob.pickLineItems().stream()
            .map(handoverJobLineItem -> new OrderLineDispatched()
                .withOrderId(source.getTenantOrderId())
                .withCarrierName(carrier.name())
                .withDispatchDate(dispatchDate)
                .withOrangePrinted(dispatchDate)
                .withEan(handoverJobLineItem.getArticle().getEan())
                .withQuantity(handoverJobLineItem.getPicked())
                .withWarehouse(warehouse)
                .withStoreId(locationChainCode)
                .withTrackingNumber(carrierTrackingNumber)
                .withReturnShipmentId(returnShipmentId)
                .withPackageReference(null))
            .filter(orderLineDispatched -> orderLineDispatched.getQuantity() > 0)
            .collect(Collectors.toList());
    }

    /**
     * Converts ROUTING_PLAN_NOT_ROUTABLE.
     *
     * @param source fulfillment event
     * @return OMS event
     */
    public OrderPartRejected convert(RoutingPlanNotRoutable source) {
        return orderPartsRejectedConverter.convert(source);
    }

    /**
     * Converts EnrichedPickJobAborted to OrderPartRejected.
     *
     * @param enrichedPickJobAborted fulfillment event
     * @return OMS event
     */
    public OrderPartRejected convert(EnrichedPickJobAborted enrichedPickJobAborted) {
        return orderPartsRejectedConverter.convert(enrichedPickJobAborted);
    }

    /**
     * Converts EnrichedOrderCancelled to OrderPartRejected.
     *
     * @param source fulfillment event
     * @return OMS event
     */
    public OrderPartRejected convert(OrderCancelled source) {
        return orderPartsRejectedConverter.convert(source);
    }

    /**
     * Converts OrderPartsCancelled.
     *
     * @param enrichedPickJobPickingFinished fulfillment event
     * @return OMS event
     */
    public Optional<OrderPartsCancelled> convert(EnrichedPickJobPickingFinished enrichedPickJobPickingFinished) {
        var source = enrichedPickJobPickingFinished.fulfillmentEvent();

        var orderLines = IntStream.range(0, source.getPickLineItems().size())
            .mapToObj(i -> convertCancelledLine(i, source.getPickLineItems().get(i)))
            .filter(Objects::nonNull)
            .toList();

        var orderPartsCancelled = new OrderPartsCancelled()
            .withOrderId(source.getTenantOrderId())
            .withWarehouse(ConverterUtils.toWarehouse(enrichedPickJobPickingFinished.facility()))
            .withCancellationDate(dateUtils.asZonedDateTime(source.getCreated()))
            .withOrderLines(orderLines.stream()
                .flatMap(Optional::stream)
                .collect(Collectors.toList()))
            .withIsTest(false);

        if (!orderPartsCancelled.getOrderLines().isEmpty()) {
            return Optional.of(orderPartsCancelled);
        }
        return Optional.empty();
    }

    private Optional<com.bestseller.generated.interfacecontracts.kafkamessages.pojos.
        orderPartsCancelled.OrderLine> convertCancelledLine(int lineNumber, LineItem lineItem) {

        var unpickedItemsQuantity = lineItem.getQuantity() - lineItem.getPicked();

        if (unpickedItemsQuantity > 0) {
            return Optional.of(new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderLine()
                .withEan(lineItem.getArticle().getEan())
                .withQuantity(unpickedItemsQuantity)
                .withLineNumber(lineNumber + 1)
                .withCancelReason(ITEM_NOT_AVAILABLE.name()));
        }
        return Optional.empty();
    }

    private boolean carrierIsBring(Carrier carrier) {
        return BRING.equalsIgnoreCase(carrier.name());
    }

}
