package com.bestseller.storeorderfulfillmentservice.converter;

import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.CustomAttributes;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.ArticleAttribute;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.PackJobCreated;
import com.bestseller.storeorderfulfillmentservice.configuration.TemplateCarrierMappingProperties;
import com.bestseller.storeorderfulfillmentservice.model.order.CustomerDetails;
import com.bestseller.storeorderfulfillmentservice.model.order.OrderDetails;
import com.bestseller.storeorderfulfillmentservice.model.order.OrderLine;
import com.bestseller.storeorderfulfillmentservice.model.order.StoreDetails;
import com.neovisionaries.i18n.CountryCode;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Clock;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * PackJobCreated event to OrderDetails converter.
 */
@Component
@RequiredArgsConstructor
public class OrderDetailsConverter implements Converter<PackJobCreated, OrderDetails> {

    private static final String SIZE_LENGTH = "SIZE~LENGTH";
    private static final int CENT_DIGITS = 2;
    private static final DateTimeFormatter DATE_SPLIT_BY_DOTS_FORMATTER = DateTimeFormatter.ofPattern("dd.MM.yyyy");

    private final Clock clock;
    private final TemplateCarrierMappingProperties templateCarrierMappingProperties;

    @Override
    public OrderDetails convert(PackJobCreated source) {
        var customerInformation = source.getRecipient();
        var orderLines = source.getLineItems()
            .stream()
            .map(orderItem -> {
                var itemAttribute = orderItem.getArticle()
                    .getAttributes()
                    .stream()
                    .collect(Collectors.toMap(ArticleAttribute::getKey, ArticleAttribute::getValue));
                var size = itemAttribute.containsKey(SIZE_LENGTH)
                        ? itemAttribute.get(SIZE_LENGTH) : itemAttribute.get("SIZE");
                return OrderLine.builder()
                    .quantity(orderItem.getQuantity())
                    .description(orderItem.getArticle().getTitle())
                    .ean(orderItem.getArticle().getEan())

                    // In order to reduce api calls to FFT, we should include these attributes in the placement moment.
                    .price(calculatePrice(itemAttribute))
                    .styleOption(itemAttribute.get("STYLE_OPTION"))
                    .size(size)
                    .build();
            })
            .toList();

        var itemsPrice = orderLines.stream()
            .map(orderLine -> orderLine.price().multiply(new BigDecimal(orderLine.quantity())))
            .reduce(BigDecimal::add)
            .orElseThrow();


        return OrderDetails.builder()
            .processId(source.getProcessId())
            .orderNumber(source.getTenantOrderId())
            .customerDetails(CustomerDetails
                .builder()
                .firstName(customerInformation.getFirstName())
                .lastname(customerInformation.getLastName())
                .street(customerInformation.getStreet())
                .houseNumber(customerInformation.getHouseNumber())
                .postalCode(customerInformation.getPostalCode())
                .countryCode(customerInformation.getCountry())
                .country(CountryCode.getByCode(customerInformation.getCountry(), false).getName())
                .city(customerInformation.getCity())
                .build())
            .orderLines(orderLines)
            .storeDetails(StoreDetails.builder()
                .facilityReference(source.getFacilityRef())
                .build())
            .placementDate(DATE_SPLIT_BY_DOTS_FORMATTER.format(source.getOrderDate().atZone(clock.getZone())))
            .shipmentDate(DATE_SPLIT_BY_DOTS_FORMATTER.format(source.getCreated().atZone(clock.getZone())))
            .itemsPrice(itemsPrice)
            .pickJobReference(source.getPickJobRef())
            .brand(Optional.ofNullable(source.getCustomAttributes())
                .map(CustomAttributes::getBrand)
                .orElse(null))
            .carrier(templateCarrierMappingProperties.getTemplateCarrierValue(source.getCarrierKey()))
            .build();
    }

    private BigDecimal calculatePrice(Map<String, String> itemAttribute) {
        return new BigDecimal(itemAttribute.get("PRICE"))
            .subtract(new BigDecimal(itemAttribute.getOrDefault("DISCOUNT_VALUE", "0.0")))
            .setScale(CENT_DIGITS, RoundingMode.CEILING);
    }
}
