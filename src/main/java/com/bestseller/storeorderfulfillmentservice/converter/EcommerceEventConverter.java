package com.bestseller.storeorderfulfillmentservice.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPart;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.storeorderfulfillmentservice.configuration.Carrier;
import com.bestseller.storeorderfulfillmentservice.configuration.CarrierMappingProperties;
import com.bestseller.storeorderfulfillmentservice.model.DeliveryType;
import com.bestseller.storeorderfulfillmentservice.model.ServiceType;
import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.DeliveryPreferences;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.ServiceOption;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.Shipping;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.ecom.EnrichedOrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.Article;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.Attribute;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.BrandTag;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerInformation;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.CustomAttributes;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.FulfillmentOrderLine;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.OrderCreation;
import com.bestseller.storeorderfulfillmentservice.model.pcs.ProductInformation;
import com.bestseller.storeorderfulfillmentservice.service.AddressConverterFinderService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Clock;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bestseller.storeorderfulfillmentservice.converter.AttributeUtils.getAttribute;
import static com.bestseller.storeorderfulfillmentservice.converter.AttributeUtils.getAttributeOrThrow;
import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.Category.DESCRIPTIVE;
import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.Category.MISCELLANEOUS;

/**
 * Convert from OrderPartsCreated to List of OrderLineExported.
 */
@Slf4j
@Component
@RequiredArgsConstructor
@SuppressWarnings("PMD.TooManyMethods")
public class EcommerceEventConverter {

    // This field will appear below to the product name in fulfillment tools interface.
    private static final String PRODUCT_DESCRIPTION_SUBTITLE = "%%subtitle%%";

    private final Clock clock;

    private final AddressConverterFinderService addressConverterFinderService;

    private final MeterRegistry meterRegistry;

    private final CarrierMappingProperties carrierMappingProperties;

    private final OrderPartsRejectedConverter orderPartsRejectedConverter;

    /**
     * Convert OrderPartsCreated to List of OrderLineExported.
     *
     * @param source as OrderPartsCreated
     * @return List of OrderLineExported
     */
    public List<OrderLineExported> convert(OrderPartsCreated source) {
        return getOrderLineStream(source)
            .map(orderLine -> new OrderLineExported()
                .withOrderId(source.getOrderId())
                .withQuantity(orderLine.getQuantity())
                .withWarehouse(source.getFulfillmentNode())
                .withEan(orderLine.getEan())
                .withExportDate(clock.instant().atZone(clock.getZone())))
            .toList();
    }

    /**
     * Convert OrderPartsCreated to OrderCreation.
     *
     * @param source as EnrichedOrderPartsCreated
     * @return OrderCreation
     */
    public OrderCreation convertToOrderCreation(EnrichedOrderPartsCreated source) {
        var orderPartsCreated = source.getOrderPartsCreated();
        var shippingAddress = orderPartsCreated.getShippingInformation().getShippingAddress();

        fixHouseNumber(shippingAddress);

        DeliveryOptionQuery deliveryOptionQuery = DeliveryOptionQuery.builder()
            .country(orderPartsCreated.getShippingInformation().getShippingAddress().getCountry())
            .deliveryType(DeliveryType.getDeliveryType(orderPartsCreated.getOrderDetails().getCarrierVariant()))
            .serviceType(ServiceType.getServiceType(orderPartsCreated.getOrderDetails().getShippingMethod()))
            .carrier(orderPartsCreated.getOrderDetails().getCarrier())
            .build();

        Carrier carrier = carrierMappingProperties.getCarrier(deliveryOptionQuery);
        var order = OrderCreation.builder()
            .orderDate(orderPartsCreated.getPlacedDate().toInstant())
            .tenantOrderId(orderPartsCreated.getOrderId())
            .deliveryPreferences(DeliveryPreferences.builder()
                .shipping(Shipping.builder()
                    .preferredCarriers(getPreferredCarriers(carrier))
                    .preferredCarriersWithProduct(getPreferredCarriersWithProduct(carrier))
                    .build())
                .build())
            .customAttributes(CustomAttributes.builder()
                .shipmentCost(orderPartsCreated.getOrderDetails().getShippingFees())
                .brand(orderPartsCreated.getBrand())
                .build())
            .consumer(ConsumerInformation.builder()
                .addresses(addressConverterFinderService.getAddressService(deliveryOptionQuery)
                    .generateAddresses(source.getOrderPartsCreated(), deliveryOptionQuery))
                .email(orderPartsCreated.getCustomerInformation().getEmail())
                .build())
            .orderLineItems(getOrderLineStream(orderPartsCreated)
                .map(mapOrderLines(source))
                .collect(Collectors.toList()))
            .build();

        getOrderCounter(orderPartsCreated).increment();
        return order;
    }

    public OrderPartRejected rejectOrder(EnrichedOrderPartsCreated source) {
        return orderPartsRejectedConverter.convert(source.getOrderPartsCreated());
    }

    private Function<OrderLine, FulfillmentOrderLine> mapOrderLines(EnrichedOrderPartsCreated source) {
        return orderLine -> {
            var productInformation = source.getProductsInformation().get(orderLine.getEan());
            List<Attribute> attributes = new ArrayList<>();
            // This category will be showed on FFT app
            attributes.add(getAttribute(DESCRIPTIVE, PRODUCT_DESCRIPTION_SUBTITLE, productInformation.styleNumber()));
            attributes.add(getAttribute(DESCRIPTIVE, "COLOR", productInformation.color()));

            if (StringUtils.isNotEmpty(productInformation.length())) {
                final String sizeLengthValue = "%s ~ %s".formatted(productInformation.size(), productInformation.length());
                attributes.add(getAttribute(DESCRIPTIVE, "SIZE~LENGTH", sizeLengthValue));
            } else {
                attributes.add(getAttribute(DESCRIPTIVE, "SIZE", productInformation.size()));
            }

            attributes.add(getAttributeOrThrow(DESCRIPTIVE, "EAN", orderLine.getEan()));
            attributes.add(getAttributeOrThrow(DESCRIPTIVE, "PRICE", calculatePrice(orderLine).toPlainString()));

            // This category will not be showed on FFT app
            attributes.add(getAttributeOrThrow(MISCELLANEOUS, "STYLE_OPTION", productInformation.styleOption()));
            attributes.add(getAttributeOrThrow(MISCELLANEOUS, "EDI_STYLE_NAME", productInformation.ediStyleName()));
            if (productInformation.brand() != null) {
                attributes.add(getAttribute(MISCELLANEOUS, "BRAND", productInformation.brand()));
            }

            // Create the brand tag for the order line in lower case
            List<BrandTag> brandTags = new ArrayList<>();
            getBrandTag(productInformation.brandAbbreviation())
                .ifPresent(brandTags::add);

            FulfillmentOrderLine.FulfillmentOrderLineBuilder orderLineBuilder = FulfillmentOrderLine.builder()
                .article(Article.builder()
                    .ean(orderLine.getEan())
                    .imageUrl(getImageUrl(productInformation))
                    .title(orderLine.getProductName())
                    .attributes(attributes.stream().filter(Objects::nonNull).toList())
                    .build())
                .quantity(orderLine.getQuantity())
                .shopPrice(orderLine.getRetailPrice())
                .scannableCodes(List.of(orderLine.getEan()));
            //add the brand tag attribute only if the brand exists
            if (!brandTags.isEmpty()) {
                orderLineBuilder.tags(brandTags);
            }
            return orderLineBuilder.build();

        };
    }

    private void fixHouseNumber(Address shippingAddress) {
        //The house number is mandatory in fulfillment tools, that is why we set it to empty string if it is null.
        if (shippingAddress.getHouseNumber() == null || shippingAddress.getHouseNumber().isBlank()) {
            shippingAddress.setHouseNumber(" ");
        }
    }

    /**
     * Report metrics with billing and shipping names are different with correct tags.
     */
    private Counter getOrderCounter(OrderPartsCreated orderPartsCreated) {
        var shippingAddress = orderPartsCreated.getShippingInformation().getShippingAddress();
        return meterRegistry.counter(
            "order.count", Tags.of(
                "carrier", orderPartsCreated.getOrderDetails().getCarrier(),
                "shippingcountry", shippingAddress.getCountry()
            )
        );
    }

    private String getImageUrl(ProductInformation productInformation) {
        if (productInformation.imageReferencesUrls() == null || productInformation.imageReferencesUrls().isEmpty()) {
            log.warn("EAN {} has no image. Order will be processed.", productInformation.ean());
            return null;
        }
        return productInformation.imageReferencesUrls().get(0);
    }

    private Stream<OrderLine> getOrderLineStream(OrderPartsCreated source) {
        return source.getOrderParts()
            .stream()
            .map(OrderPart::getOrderLines)
            .flatMap(Collection::stream);
    }

    private Optional<BrandTag> getBrandTag(String brand) {
        if (StringUtils.isEmpty(brand)) {
            return Optional.empty();
        }
        return Optional.of(BrandTag.builder()
            .id("brands")
            .value(brand.toLowerCase(Locale.ROOT))
            .build());
    }

    private BigDecimal calculatePrice(OrderLine orderLine) {
        final BigDecimal discountValue = orderLine.getDiscountValue() == null
            ? BigDecimal.ZERO
            : orderLine.getDiscountValue();
        return orderLine.getRetailPrice().subtract(discountValue);
    }

    @SuppressWarnings("PMD.ReturnEmptyCollectionRatherThanNull")
    private static List<String> getPreferredCarriers(Carrier carrier) {
        if (hasNoCarrierServices(carrier)) {
            return List.of(carrier.key());
        }
        return null;
    }

    @SuppressWarnings("PMD.ReturnEmptyCollectionRatherThanNull")
    private static List<ServiceOption> getPreferredCarriersWithProduct(Carrier carrier) {
        if (hasNoCarrierServices(carrier)) {
            return null;
        }
        return List.of(
            ServiceOption.builder()
                .carrierKey(carrier.key())
                .carrierServices(carrier.services())
                .build()
        );
    }

    private static boolean hasNoCarrierServices(Carrier carrier) {
        return carrier.services() == null || carrier.services().isEmpty();
    }
}
