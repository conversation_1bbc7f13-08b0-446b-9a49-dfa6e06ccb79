package com.bestseller.storeorderfulfillmentservice.converter;

import com.bestseller.storeorderfulfillmentservice.exception.AttributeNotFoundException;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.Attribute;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.Category;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

@UtilityClass
public final class AttributeUtils {

    public static Attribute getAttribute(Category category, String attributeName, String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        return Attribute.builder()
            .category(category)
            .key(attributeName)
            .value(value)
            .build();
    }

    /**
     * Returns an attribute or throws {@link AttributeNotFoundException} if it is not found.
     */
    public static Attribute getAttributeOrThrow(Category category, String attributeName, String value) {
        return Optional.ofNullable(getAttribute(category, attributeName, value))
            .orElseThrow(() -> new AttributeNotFoundException(attributeName));
    }
}
