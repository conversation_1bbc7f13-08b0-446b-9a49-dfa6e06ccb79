package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;

import java.util.List;

public interface AddressService {

    List<ConsumerAddress> generateAddresses(OrderPartsCreated orderPartsCreated, DeliveryOptionQuery deliveryOptionQuery);

    List<ConsumerAddress> generateHomeDeliveryAddresses(OrderPartsCreated orderPartsCreated);

    List<ConsumerAddress> generatePickupAddresses(OrderPartsCreated orderPartsCreated);

    List<ConsumerAddress> generateLockerAddresses(OrderPartsCreated orderPartsCreated);

    String getCountry();

    String getCarrier();

    String getDeliveryType();
}
