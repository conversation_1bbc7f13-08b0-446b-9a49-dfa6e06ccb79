package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.exception.AddressNotImplementToCountry;
import com.bestseller.storeorderfulfillmentservice.model.DeliveryType;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;

import java.util.List;

public abstract class NetherlandsPickupAddressService extends NetherlandsAddressService {

    @Override
    public List<ConsumerAddress> generateHomeDeliveryAddresses(OrderPartsCreated orderPartsCreated) {
        throw new AddressNotImplementToCountry(getCountry(), "forHome");
    }

    @Override
    public String getDeliveryType() {
        return DeliveryType.PICKUP.name();
    }
}
