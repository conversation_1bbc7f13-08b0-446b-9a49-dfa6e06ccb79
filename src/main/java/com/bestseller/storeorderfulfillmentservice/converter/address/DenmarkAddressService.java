package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import com.neovisionaries.i18n.CountryCode;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getBillingAddress;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getPhoneNumbers;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getShippingAddress;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.mapName;

@Service
public class DenmarkAddressService extends BaseAddressService {

    private static final String GLS_DK_PARCEL_LOCKER_PREFIX = "208";
    private static final int GLC_DK_PARCEL_LOCKER_ID_LENGTH = 10;
    private static final String GLS_CARRIER = "GLS";

    @Override
    public List<ConsumerAddress> generatePickupAddresses(OrderPartsCreated orderPartsCreated) {
        var shippingAddress = getShippingAddress(orderPartsCreated);
        var billingAddress = getBillingAddress(orderPartsCreated);
        return List.of(
            mapName(shippingAddress)
                .phoneNumbers(getPhoneNumbers(shippingAddress))
                .country(shippingAddress.getCountry())
                .city(shippingAddress.getCity())
                .postalCode(shippingAddress.getZipcode())
                .street(shippingAddress.getAddressLine1())
                .additionalAddressInfo(
                    formatParcelLockerId(
                        orderPartsCreated.getOrderDetails().getCarrier(),
                        getShippingAddress(orderPartsCreated).getAddressLine3())
                )
                .houseNumber(shippingAddress.getHouseNumber())
                .addressType(ConsumerAddress.AddressType.PARCEL_LOCKER)
                .build(),
            mapName(shippingAddress)
                .phoneNumbers(getPhoneNumbers(shippingAddress))
                .country(billingAddress.getCountry())
                .city(billingAddress.getCity())
                .postalCode(billingAddress.getZipcode())
                .street(billingAddress.getAddressLine1())
                .additionalAddressInfo(getShippingAddress(orderPartsCreated).getAddressLine3())
                .houseNumber(billingAddress.getHouseNumber())
                .addressType(ConsumerAddress.AddressType.INVOICE_ADDRESS)
                .build()
        );
    }

    @Override
    public String getCountry() {
        return CountryCode.DK.getAlpha2();
    }

    private static String formatParcelLockerId(String carrier, String parcelLockerId) {
        if (!GLS_CARRIER.equalsIgnoreCase(carrier)) {
            return parcelLockerId;
        }

        if (parcelLockerId == null || parcelLockerId.isBlank()) {
            return null;
        }

        if (parcelLockerId.startsWith(GLS_DK_PARCEL_LOCKER_PREFIX)
            && parcelLockerId.length() >= GLC_DK_PARCEL_LOCKER_ID_LENGTH) {
            return parcelLockerId;
        }

        return GLS_DK_PARCEL_LOCKER_PREFIX + String.format("%07d", Integer.parseInt(parcelLockerId));
    }

}
