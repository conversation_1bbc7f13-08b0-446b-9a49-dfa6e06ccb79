package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.common.AdditionalInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.ShippingInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.exception.AddressNotImplement;
import com.bestseller.storeorderfulfillmentservice.exception.InvalidMessageException;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public abstract class GermanyDHLNotHomeAddressService extends GermanyAddressService {

    protected static final String POST_NUMBER_KEY = "carrierCustomerNumber";

    private static final String DHL = "DHL";

    @Override
    public String getCarrier() {
        return DHL;
    }

    @Override
    public List<ConsumerAddress> generateLockerAddresses(OrderPartsCreated orderPartsCreated) {
        throw new AddressNotImplement(getCountry(), "Locker delivery", getCarrier());
    }

    @Override
    public List<ConsumerAddress> generateHomeDeliveryAddresses(OrderPartsCreated orderPartsCreated) {
        throw new AddressNotImplement(getCountry(), "Home delivery", getCarrier());
    }

    @Override
    public List<ConsumerAddress> generatePickupAddresses(OrderPartsCreated orderPartsCreated) {
        throw new AddressNotImplement(getCountry(), "Pickup delivery", getCarrier());
    }

    protected Optional<AdditionalInformation> getPostNumberKey(OrderPartsCreated orderPartsCreated) {
        return orderPartsCreated
            .getShippingInformation()
            .getAdditionalInformation()
            .stream()
            .filter(additionalInformation -> POST_NUMBER_KEY.equals(additionalInformation.getKey()))
            .findFirst();
    }

    protected void validateRequiredFields(OrderPartsCreated orderPartsCreated) {
        ShippingInformation shippingInformation = orderPartsCreated.getShippingInformation();
        if (shippingInformation.getAdditionalInformation() == null) {
            throw new InvalidMessageException("Additional information is missing in the OrderPartsCreated message.");
        }
        if (shippingInformation.getShippingAddress().getAddressLine3().isBlank()) {
            throw new InvalidMessageException("AddressLine3 is missing on the shipping address.");
        }
    }
}
