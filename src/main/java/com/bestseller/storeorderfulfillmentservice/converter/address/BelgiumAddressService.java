package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import com.neovisionaries.i18n.CountryCode;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getBillingAddress;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getPhoneNumbers;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getShippingAddress;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.mapAddress;
import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress.AddressType.INVOICE_ADDRESS;
import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress.AddressType.PARCEL_LOCKER;

@Service
public class BelgiumAddressService extends BaseAddressService {

    @Override
    public List<ConsumerAddress> generatePickupAddresses(OrderPartsCreated orderPartsCreated) {
        return getAddresses(orderPartsCreated);
    }

    @Override
    public List<ConsumerAddress> generateLockerAddresses(OrderPartsCreated orderPartsCreated) {
        return getAddresses(orderPartsCreated);
    }

    @Override
    public String getCountry() {
        return CountryCode.BE.getAlpha2();
    }

    private static List<ConsumerAddress> getAddresses(OrderPartsCreated orderPartsCreated) {
        var shippingAddress = getShippingAddress(orderPartsCreated);
        return List.of(
            mapAddress(shippingAddress)
                .phoneNumbers(getPhoneNumbers(shippingAddress))
                .additionalAddressInfo(getShippingAddress(orderPartsCreated).getAddressLine3())
                .addressType(PARCEL_LOCKER)
                .build(),
            mapAddress(getBillingAddress(orderPartsCreated))
                .addressType(INVOICE_ADDRESS)
                .build()
        );
    }

}
