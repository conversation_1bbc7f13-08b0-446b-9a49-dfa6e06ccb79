package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.PhoneNumber;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@UtilityClass
public class AddressUtils {

    public static Address getBillingAddress(OrderPartsCreated orderPartsCreated) {
        return orderPartsCreated.getCustomerInformation().getBillingAddress();
    }

    public static Address getShippingAddress(OrderPartsCreated orderPartsCreated) {
        return orderPartsCreated.getShippingInformation().getShippingAddress();
    }

    public static List<PhoneNumber> getPhoneNumbers(Address shippingAddress) {
        List<PhoneNumber> phoneNumbers = new ArrayList<>();
        if (StringUtils.isNotEmpty(shippingAddress.getPhoneNumber())) {
            phoneNumbers.add(PhoneNumber.builder()
                .type("MOBILE")
                .value(shippingAddress.getPhoneNumber())
                .build());
        }
        return phoneNumbers;
    }

    public static ConsumerAddress.ConsumerAddressBuilder<?, ?> mapName(Address address) {
        return ConsumerAddress.builder()
            .firstName(address.getFirstName())
            .lastName(address.getLastName());
    }

    public static ConsumerAddress.ConsumerAddressBuilder<?, ?> mapAddress(Address address) {
        return mapName(address)
            .street(address.getAddressLine1())
            .houseNumber(address.getHouseNumber())
            .postalCode(address.getZipcode())
            .city(address.getCity())
            .country(address.getCountry());
    }
}
