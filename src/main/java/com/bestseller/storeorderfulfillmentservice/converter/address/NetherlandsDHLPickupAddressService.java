package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getBillingAddress;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getPhoneNumbers;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getShippingAddress;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.mapAddress;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.mapName;
import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress.AddressType.INVOICE_ADDRESS;
import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress.AddressType.PARCEL_LOCKER;

@Service
public class NetherlandsDHLPickupAddressService extends NetherlandsPickupAddressService {

    @Override
    public List<ConsumerAddress> generatePickupAddresses(OrderPartsCreated orderPartsCreated) {
        var shippingAddress = getShippingAddress(orderPartsCreated);
        var billingAddress = getBillingAddress(orderPartsCreated);
        return List.of(
            mapAddress(billingAddress)
                .addressType(INVOICE_ADDRESS)
                .build(),
            mapName(shippingAddress)
                .phoneNumbers(getPhoneNumbers(shippingAddress))
                .street(billingAddress.getAddressLine1())
                .houseNumber(billingAddress.getHouseNumber())
                .postalCode(billingAddress.getZipcode())
                .city(billingAddress.getCity())
                .country(billingAddress.getCountry())
                .additionalAddressInfo(getShippingAddress(orderPartsCreated).getAddressLine3())
                .addressType(PARCEL_LOCKER)
                .build()
        );
    }

    @Override
    public String getCarrier() {
        return "DHL";
    }
}
