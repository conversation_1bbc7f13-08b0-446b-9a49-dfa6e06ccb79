package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.common.AdditionalInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.DeliveryType;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getPhoneNumbers;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getShippingAddress;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.mapAddress;
import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress.AddressType.POSTAL_ADDRESS;

@Service
public class GermanyDHLPostfilialeAddressService extends GermanyDHLNotHomeAddressService {

    private static final String POSTFILIALE = "Postfiliale";

    @Override
    public String getDeliveryType() {
        return DeliveryType.PICKUP.name();
    }

    @Override
    public List<ConsumerAddress> generatePickupAddresses(OrderPartsCreated orderPartsCreated) {
        validateRequiredFields(orderPartsCreated);

        Address shippingAddress = getShippingAddress(orderPartsCreated);
        String postfilialeNumber = shippingAddress.getAddressLine3();

        // Post Number is optional accordingly to DHL API
        String postNumber = getPostNumberKey(orderPartsCreated)
            .map(AdditionalInformation::getValue)
            .orElse(null);

        ConsumerAddress mappedAddress = mapAddress(shippingAddress)
            .street(POSTFILIALE)
            .houseNumber(postfilialeNumber)
            .phoneNumbers(getPhoneNumbers(shippingAddress))
            .addressType(POSTAL_ADDRESS)
            .build();

        if (postNumber != null) {
            mappedAddress.setAdditionalAddressInfo(postNumber);
        }

        return List.of(mappedAddress);
    }
}
