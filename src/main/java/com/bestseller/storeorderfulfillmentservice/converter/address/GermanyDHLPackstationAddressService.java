package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.exception.KeyNotFoundException;
import com.bestseller.storeorderfulfillmentservice.model.DeliveryType;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getPhoneNumbers;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getShippingAddress;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.mapAddress;
import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress.AddressType.POSTAL_ADDRESS;

@Service
public class GermanyDHLPackstationAddressService extends GermanyDHLNotHomeAddressService {

    private static final String PACKSTATION = "Packstation";

    @Override
    public String getDeliveryType() {
        return DeliveryType.LOCKER.name();
    }

    @Override
    public List<ConsumerAddress> generateLockerAddresses(OrderPartsCreated orderPartsCreated) {
        validateRequiredFields(orderPartsCreated);

        Address shippingAddress = getShippingAddress(orderPartsCreated);
        String packstationNumber = shippingAddress.getAddressLine3();

        String postNumber = getPostNumberKey(orderPartsCreated)
            .orElseThrow(() -> new KeyNotFoundException(POST_NUMBER_KEY))
            .getValue();

        return List.of(
            mapAddress(shippingAddress)
                .street(PACKSTATION)
                .houseNumber(packstationNumber)
                .phoneNumbers(getPhoneNumbers(shippingAddress))
                .additionalAddressInfo(postNumber)
                .addressType(POSTAL_ADDRESS)
                .build());
    }

}
