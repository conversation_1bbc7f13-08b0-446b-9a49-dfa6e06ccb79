package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.exception.AddressNotImplementToCountry;
import com.bestseller.storeorderfulfillmentservice.exception.ImpossibleToPlaceOrderException;
import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import lombok.AllArgsConstructor;
import lombok.Generated;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getPhoneNumbers;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.getShippingAddress;
import static com.bestseller.storeorderfulfillmentservice.converter.address.AddressUtils.mapAddress;
import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress.AddressType.POSTAL_ADDRESS;

@AllArgsConstructor
@Slf4j
public abstract class BaseAddressService implements AddressService {

    private static final String ANY = "*";

    /**
     * This method is 100% covered but for some reason jacoco doesn't understand.
     * That's why it's marked with @Generated.
     */
    @Override
    @Generated
    public List<ConsumerAddress> generateAddresses(OrderPartsCreated orderPartsCreated,
                                                   DeliveryOptionQuery deliveryOptionQuery) {
        return switch (deliveryOptionQuery.deliveryType()) {
            case HOME -> generateHomeDeliveryAddresses(orderPartsCreated);
            case PICKUP -> generatePickupAddresses(orderPartsCreated);
            case LOCKER -> generateLockerAddresses(orderPartsCreated);
            default -> throw new ImpossibleToPlaceOrderException("Post office is not implemented");
        };
    }


    /**
     * This logic was just re-written. There's a bug regarding packstation addresses remaining.
     *
     * @param orderPartsCreated
     * @return
     */
    @Override
    public List<ConsumerAddress> generatePickupAddresses(OrderPartsCreated orderPartsCreated) {
        return getPostalCustomerAddress(orderPartsCreated);
    }

    @Override
    public List<ConsumerAddress> generateHomeDeliveryAddresses(OrderPartsCreated orderPartsCreated) {
        return getPostalCustomerAddress(orderPartsCreated);
    }

    protected static List<ConsumerAddress> getPostalCustomerAddress(OrderPartsCreated orderPartsCreated) {
        return List.of(
            mapAddress(getShippingAddress(orderPartsCreated))
                .phoneNumbers(getPhoneNumbers(getShippingAddress(orderPartsCreated)))
                .additionalAddressInfo(getShippingAddress(orderPartsCreated).getAddressLine3())
                .addressType(POSTAL_ADDRESS)
                .build());
    }

    @Override
    public List<ConsumerAddress> generateLockerAddresses(OrderPartsCreated orderPartsCreated) {
        throw new AddressNotImplementToCountry(getCountry(), "forLocker");
    }

    @Override
    public String getCarrier() {
        return ANY;
    }

    @Override
    public String getDeliveryType() {
        return ANY;
    }

}
