package com.bestseller.storeorderfulfillmentservice.converter.address;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.exception.AddressNotImplementToCountry;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.ConsumerAddress;
import com.neovisionaries.i18n.CountryCode;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class NorwayAddressService extends BaseAddressService {

    @Override
    public List<ConsumerAddress> generateHomeDeliveryAddresses(OrderPartsCreated orderPartsCreated) {
        throw new AddressNotImplementToCountry(getCountry(), "forHomeDelivery");
    }

    @Override
    public String getCountry() {
        return CountryCode.NO.getAlpha2();
    }
}
