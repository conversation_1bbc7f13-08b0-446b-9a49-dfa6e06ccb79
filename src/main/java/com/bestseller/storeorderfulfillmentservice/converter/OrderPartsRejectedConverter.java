package com.bestseller.storeorderfulfillmentservice.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPart;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.LineItem;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.OrderCancelled;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.notroutable.RoutingPlanNotRoutable;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedPickJobAborted;
import com.bestseller.storeorderfulfillmentservice.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.Clock;
import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import java.util.stream.IntStream;

import static com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.CancellationReason.ITEM_NOT_AVAILABLE;

@Component
@RequiredArgsConstructor
public class OrderPartsRejectedConverter {

    private final Clock clock;

    private final DateUtils dateUtils;

    public OrderPartRejected convert(OrderCancelled source) {
        return createOrderPartsRejected(
            source.getTenantOrderId(),
            source.getConsumer().getAddresses().get(0).getCountry(),
            source.getCreated(),
            convertFulfilmentToolsOrderLineToOrderLine(source.getOrderLineItems()));
    }

    public OrderPartRejected convert(EnrichedPickJobAborted enrichedPickJobAborted) {
        var source = enrichedPickJobAborted.pickJobAborted();
        return createOrderPartsRejected(
            source.getTenantOrderId(),
            enrichedPickJobAborted.facility().address().getCountry(),
            source.getCreated(),
            convertFulfilmentToolsOrderLineToOrderLine(source.getPickLineItems()));
    }

    public OrderPartRejected convert(RoutingPlanNotRoutable source) {
        return createOrderPartsRejected(
            source.getTenantOrderId(),
            source.getTargetAddress().getCountry(),
            source.getCreated(),
            convertFulfilmentToolsOrderLineToOrderLine(source.getOrderLineItems()));
    }

    public OrderPartRejected convert(OrderPartsCreated orderPartsCreated) {
        List<com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine> orderLines =
            orderPartsCreated.getOrderParts()
                .stream()
                .map(OrderPart::getOrderLines)
                .flatMap(Collection::stream)
                .toList();

        return createOrderPartsRejected(
            orderPartsCreated.getOrderId(),
            clock.instant(),
            convertEcomOrderLineToOrderLine(orderLines))
            .withWarehouse(orderPartsCreated.getFulfillmentNode());
    }

    private OrderPartRejected createOrderPartsRejected(String orderId,
                                                       String country,
                                                       Instant created,
                                                       List<OrderLine> orderLines) {
        return createOrderPartsRejected(orderId, created, orderLines)
            .withWarehouse(ConverterUtils.toWarehouse(country));
    }

    private OrderPartRejected createOrderPartsRejected(String orderId,
                                                       Instant created,
                                                       List<OrderLine> orderLines) {
        return new OrderPartRejected()
            .withOrderId(orderId)
            .withCancellationDate(dateUtils.asZonedDateTime(created))
            .withIsTest(false)
            .withOrderLines(orderLines);
    }

    private OrderLine convertLine(int lineNumber, String ean, int quantity) {
        return new OrderLine()
            .withEan(ean)
            .withQuantity(quantity)
            .withLineNumber(lineNumber + 1)
            .withCancelReason(ITEM_NOT_AVAILABLE.name());
    }

    private List<OrderLine> convertFulfilmentToolsOrderLineToOrderLine(List<LineItem> source) {
        return convertOrderLine(
            source,
            item -> item.getArticle().getEan(),
            LineItem::getQuantity
        );
    }

    private List<OrderLine> convertEcomOrderLineToOrderLine(
        List<com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine> source) {
        return convertOrderLine(
            source,
            com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine::getEan,
            com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.OrderLine::getQuantity
        );
    }

    private <T> List<OrderLine> convertOrderLine(
        List<T> source,
        Function<T, String> eanExtractor,
        Function<T, Integer> quantityExtractor) {

        return IntStream.range(0, source.size())
            .mapToObj(counter -> {
                var item = source.get(counter);
                return convertLine(counter, eanExtractor.apply(item), quantityExtractor.apply(item));
            })
            .toList();

    }
}
