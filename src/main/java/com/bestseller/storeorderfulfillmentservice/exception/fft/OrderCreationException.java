package com.bestseller.storeorderfulfillmentservice.exception.fft;

import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.OrderCreation;

/**
 * Exception regarding order creation in fulfillment tools.
 */
public class OrderCreationException extends RuntimeException {

    /**
     * Create this exception with this payload.
     */
    public OrderCreationException(OrderCreation payload, Throwable cause) {
        super("Couldn't submit order %s".formatted(payload), cause);
    }

}
