package com.bestseller.storeorderfulfillmentservice.exception;

/**
 * Exception thrown when a mandatory attribute is not found.
 */
public class AttributeNotFoundException extends RuntimeException {

    /**
     * Constructor.
     * @param attribute
     */
    public AttributeNotFoundException(String attribute) {
        super("Attribute %s is required but not found".formatted(attribute));
    }
}
