package com.bestseller.storeorderfulfillmentservice.exception;

import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;

public class CarrierNotSupported extends ImpossibleToPlaceOrderException {

    public CarrierNotSupported(DeliveryOptionQuery deliveryOptionQuery) {
        super("Carrier not found for given country: %s, carrier: %s, deliveryOption: %s and serviceType: %s"
            .formatted(
                deliveryOptionQuery.country(),
                deliveryOptionQuery.carrier(),
                deliveryOptionQuery.deliveryType(),
                deliveryOptionQuery.serviceType()
            ));
    }
}
