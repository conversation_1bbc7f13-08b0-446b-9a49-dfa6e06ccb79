package com.bestseller.storeorderfulfillmentservice.controller;

import com.bestseller.storeorderfulfillmentservice.model.order.OrderDetails;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.DocumentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import static com.amazonaws.services.s3.Headers.CONTENT_DISPOSITION;

/**
 * Utility endpoints for testing and troubleshooting document generation.
 */
@RestController
@RequestMapping("support/document")
@RequiredArgsConstructor
@Slf4j
public class DocumentController {
    private final DocumentService documentService;

    /**
     * Creates PDF document based on the given order details.
     */
    @PostMapping(value = "render", produces = MediaType.APPLICATION_PDF_VALUE)
    @Operation(summary = "Renders a PDF document based on the given order details.",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(content = @Content(examples = @ExampleObject("""
            {
              "storeDetails": {
                "locationChainCode": "2342",
                "brand": "Jack & Jones",
                "city": "Hamburg",
                "street": "Modering 2",
                "country": "Germany"
              },
              "customerDetails": {
                "firstName": "Amélie ",
                "lastname": "Poulain",
                "city": "Hamburg",
                "street": "Modering 1",
                "houseNumber": "56",
                "country": "Germany",
                "countryCode": "DE",
                "postalCode": "75018"
              },
              "orderNumber": "0123456789",
              "shipmentDate": "11.04.2023",
              "placementDate": "10.04.2023",
              "orderLines": [
                {
                  "description": "MIKE ORIGINAL GE 397 INDIGO KNIT COMFORT FIT JEANS",
                  "size": "32 / 32",
                  "styleOption": "12206084_BlueDenim",
                  "ean": "5715211619587",
                  "price": 59.99,
                  "quantity": 2
                }
              ],
              "itemsPrice": 199.99,
              "shippingPrice": 0.99,
              "totalPrice": 200.20,
              "brand": "JJ"
            }
             """))))
    public ResponseEntity<StreamingResponseBody> renderDocument(@RequestBody OrderDetails orderDetails) {
        String orderNumber = orderDetails.getOrderNumber();
        log.info("Start generating document for orderNumber={} from API call", orderNumber);
        return ResponseEntity.ok()
            .header(CONTENT_DISPOSITION, "Attachment; Filename=test-invoice-return-label.pdf")
            .body(outputStream -> documentService.generateDocument(orderDetails, outputStream));
    }
}
