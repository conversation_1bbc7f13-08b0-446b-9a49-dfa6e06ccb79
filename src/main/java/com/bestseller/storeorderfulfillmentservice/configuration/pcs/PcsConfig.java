package com.bestseller.storeorderfulfillmentservice.configuration.pcs;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * Configuration regarding PCS.
 */
@Configuration
public class PcsConfig {

    private static final int STREAM_BUFFER_SIZE_BYTES = 16 * 1024 * 1024;

    /**
     * PCS web client, already configured with endpoint.
     *
     * @param endpoint pcs endpoint
     * @return configured webclient for pcs
     */
    @Bean
    public WebClient pcsClient(@Value("${pcs.endpoint}") String endpoint) {
        return WebClient.builder()
                .defaultHeaders(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON))
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(STREAM_BUFFER_SIZE_BYTES))
                .baseUrl(endpoint)
                .build();
    }
}
