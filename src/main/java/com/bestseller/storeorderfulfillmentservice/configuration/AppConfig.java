package com.bestseller.storeorderfulfillmentservice.configuration;

import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.time.Clock;

/**
 * Configuration class for application.
 */
@Configuration
@EnableScheduling
@EnableAsync
@ConfigurationPropertiesScan
public class AppConfig {

    /**
     * Clock instance to use across the application.
     *
     * @return clock instance
     */
    @Bean
    public Clock utcClock() {
        return Clock.systemUTC();
    }

}
