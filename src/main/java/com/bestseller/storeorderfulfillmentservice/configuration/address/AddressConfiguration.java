package com.bestseller.storeorderfulfillmentservice.configuration.address;

import com.bestseller.storeorderfulfillmentservice.converter.address.AddressService;
import com.bestseller.storeorderfulfillmentservice.service.AddressConverterFinderService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Configuration for address conversion.
 */
@Configuration
public class AddressConfiguration {

    /**
     * Responsible for generating the {@link AddressConverterFinderService} bean.
     *
     * @param addressServices
     * @return
     */
    @Bean
    public AddressConverterFinderService addressConverterFinderService(List<AddressService> addressServices) {
        return addressServices.stream()
            .collect(Collectors.toMap(
                addressService -> AddressConverterFinderService.getIdentifier(
                    addressService.getCountry(),
                    addressService.getCarrier(),
                    addressService.getDeliveryType()),
                Function.identity(),
                (v1, v2) -> v1,
                AddressConverterFinderService::new));
    }

}
