package com.bestseller.storeorderfulfillmentservice.configuration;

import com.bestseller.storeorderfulfillmentservice.exception.CarrierNotSupported;
import com.bestseller.storeorderfulfillmentservice.model.DeliveryType;
import com.bestseller.storeorderfulfillmentservice.model.ServiceType;
import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.Locale;
import java.util.Map;
import java.util.Optional;

/**
 * Configuration class for carrier mapping between e-commerce and fulfilment tools.
 */
@Configuration
@ConfigurationProperties //Prefix is not necessary.
@PropertySource(value = "classpath:carrier-mapping.yaml", factory = YamlPropertySourceFactory.class)
@Getter
@Setter
public class CarrierMappingProperties {
    /*
     * Map of country to service type(EXPRESS or STANDARD) to delivery type(HOME, SERVICE POINT DELIVERY, etc)
     * to e-commence carrier code to Fulfilment tools carrier.
     */
    private Map<String, Map<ServiceType, Map<DeliveryType, Map<String, Carrier>>>> carrier;

    /**
     * Get Fulfilment Tools carrier for given delivery option query.
     *
     * @param deliveryOptionQuery delivery option query
     * @return Fulfilment tools carrier.
     */
    public Carrier getCarrier(DeliveryOptionQuery deliveryOptionQuery) {
        return Optional.of(carrier)
            .map(countryMap -> countryMap.get(deliveryOptionQuery.country()))
            .map(serviceTypeMapMap -> serviceTypeMapMap.get(deliveryOptionQuery.serviceType()))
            .map(deliveryTypeMapMap -> deliveryTypeMapMap.get(deliveryOptionQuery.deliveryType()))
            .map(carrierMap -> carrierMap.get(deliveryOptionQuery.carrier().toUpperCase(Locale.getDefault())))
            .orElseThrow(() -> new CarrierNotSupported(deliveryOptionQuery));
    }

}
