package com.bestseller.storeorderfulfillmentservice.configuration.pdf;

import com.openhtmltopdf.slf4j.Slf4jLogger;
import com.openhtmltopdf.util.XRLog;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration regarding the Open HTML to PDF.
 */
@Configuration
public class OpenHtmlToPdfConfiguration {

    /**
     * Define the logger.
     */
    @Bean
    public void defineLogger() {
        XRLog.setLoggerImpl(new Slf4jLogger());
    }

}
