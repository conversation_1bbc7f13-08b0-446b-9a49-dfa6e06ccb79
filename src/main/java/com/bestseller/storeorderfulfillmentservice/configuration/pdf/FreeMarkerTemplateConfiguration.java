package com.bestseller.storeorderfulfillmentservice.configuration.pdf;

import freemarker.template.Configuration;
import org.springframework.context.annotation.Bean;

import java.io.IOException;
import java.time.Clock;
import java.util.TimeZone;

import static freemarker.template.Configuration.VERSION_2_3_31;

/**
 * Configuration regarding the Freemarker Template Engine.
 */
@org.springframework.context.annotation.Configuration
public class FreeMarkerTemplateConfiguration {

    /**
     * Template configuration regarding the Freemarker Template Engine.
     * @param utcClock
     * @return configuration
     * @throws IOException
     */
    @Bean
    public Configuration templateConfiguration(Clock utcClock) throws IOException {
        Configuration configuration = new Configuration(VERSION_2_3_31);
        configuration.setClassForTemplateLoading(this.getClass(), "/template");
        configuration.setDefaultEncoding("UTF-8");
        configuration.setTimeZone(TimeZone.getTimeZone(utcClock.getZone()));
        return configuration;
    }
}
