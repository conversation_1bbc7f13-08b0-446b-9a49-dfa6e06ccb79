package com.bestseller.storeorderfulfillmentservice.configuration.actuator;

import org.springframework.boot.SpringBootVersion;
import org.springframework.boot.actuate.info.Info;
import org.springframework.boot.actuate.info.InfoContributor;
import org.springframework.core.SpringVersion;
import org.springframework.stereotype.Component;

/**
 * Spring boot version contributor, adds spring boot version into info actuator.
 */
@Component
public class SpringBootVersionContributor implements InfoContributor {

    @Override
    public void contribute(Info.Builder builder) {
        builder.withDetail("spring-version", SpringVersion.getVersion());
        builder.withDetail("springboot-version", SpringBootVersion.getVersion());
    }
}
