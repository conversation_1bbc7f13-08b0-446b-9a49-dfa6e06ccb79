package com.bestseller.storeorderfulfillmentservice.configuration.localization;

import com.bestseller.storeorderfulfillmentservice.configuration.YamlPropertySourceFactory;
import com.bestseller.storeorderfulfillmentservice.exception.LocalizationNotFound;
import com.bestseller.storeorderfulfillmentservice.model.localization.Localization;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.Map;

/**
 * Invoice Localization.
 */
@Configuration
@Getter
@Setter
@ConfigurationProperties //prefix is not mandatory. IntelliJ is wrong.
@PropertySource(value = "classpath:localization.yaml", factory = YamlPropertySourceFactory.class)
public class LocalizationConfig {

    private Map<String, Localization> localized;

    /**
     * Returns the correct localization for the invoice file.
     *
     * @param country
     * @return
     */
    public Localization getLocalizationByCountry(String country) {
        if (localized.containsKey(country)) {
            return localized.get(country);
        }
        throw new LocalizationNotFound(country);
    }

}
