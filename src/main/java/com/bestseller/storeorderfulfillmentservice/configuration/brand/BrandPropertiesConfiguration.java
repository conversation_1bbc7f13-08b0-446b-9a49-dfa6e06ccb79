package com.bestseller.storeorderfulfillmentservice.configuration.brand;

import com.bestseller.storeorderfulfillmentservice.configuration.YamlPropertySourceFactory;
import com.bestseller.storeorderfulfillmentservice.model.brand.BrandProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.HashMap;
import java.util.Optional;

@SuppressWarnings("IllegalType")
@Configuration
@ConfigurationProperties(prefix = "brand")
@PropertySource(value = "classpath:brand.yaml", factory = YamlPropertySourceFactory.class)
public class BrandPropertiesConfiguration extends HashMap<String, BrandProperty> {

    private static final String DEFAULT_KEY = "DEFAULT";

    public BrandProperty getBrandProperty(String brand) {
        return Optional.ofNullable(brand)
            .map(String::toUpperCase)
            .map(this::get)
            .orElseGet(() -> get(DEFAULT_KEY));
    }
}
