package com.bestseller.storeorderfulfillmentservice.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.Map;
import java.util.Optional;

/**
 * Configuration class for carrier mapping between e-commerce and invoice/return templates.
 */
@Configuration
@ConfigurationProperties //Prefix is not necessary.
@PropertySource(value = "classpath:template-carrier-mapping.yaml", factory = YamlPropertySourceFactory.class)
@Getter
@Setter
public class TemplateCarrierMappingProperties {

    private Map<String, String> templateCarrier;

    public String getTemplateCarrierValue(String carrierKey) {
        return Optional.of(templateCarrier)
            .map(carrierMap -> carrierMap.get(carrierKey))
            .orElse(null);
    }

}
