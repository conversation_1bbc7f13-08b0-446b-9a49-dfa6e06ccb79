package com.bestseller.storeorderfulfillmentservice.configuration.aws;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * AWS configuration class.
 */
@Configuration
@Profile({"acc", "prod"})
public class AwsConfiguration {

    /**
     * Amazon S3 bean.
     * @return
     */
    @Bean
    public AmazonS3 s3Client() {
        return AmazonS3ClientBuilder.standard().withRegion(Regions.EU_WEST_1).build();
    }
}
