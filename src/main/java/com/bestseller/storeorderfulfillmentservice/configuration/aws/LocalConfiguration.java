package com.bestseller.storeorderfulfillmentservice.configuration.aws;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * AWS S3 configuration for local environments.
 */
@Configuration
@Profile("dev")
public class LocalConfiguration {

    /**
     * AWS S3 client.
     *
     * @return amazon s3 client.
     */
    @Bean
    public AmazonS3 s3Client(@Value("${aws.local.s3.endpoint}") String awsLocalEndpoint) {
        return AmazonS3ClientBuilder.standard()
                .withEndpointConfiguration(getConfig(awsLocalEndpoint))
                // s3.deleteObject() doesn't work without creds
                .withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials("foo", "bar")))
                .withPathStyleAccessEnabled(true)
                .disableChunkedEncoding()
                .build();
    }

    private AwsClientBuilder.EndpointConfiguration getConfig(String awsLocalEndpoint) {
        return new AwsClientBuilder.EndpointConfiguration(awsLocalEndpoint, Regions.EU_WEST_1.getName());
    }
}
