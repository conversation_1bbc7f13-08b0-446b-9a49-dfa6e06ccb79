package com.bestseller.storeorderfulfillmentservice.messaging;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineExported.OrderLineExported;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.storeorderfulfillmentservice.converter.EcommerceEventConverter;
import com.bestseller.storeorderfulfillmentservice.exception.ImpossibleToPlaceOrderException;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.ecom.EnrichedOrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.service.EcommerceEnrichmentService;
import com.bestseller.storeorderfulfillmentservice.service.ValidationService;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.FulfillmentToolsService;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.OrderUploaderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Pattern;

/**
 * Message processing pipeline for order placement in fulfillment tools.
 * <p>
 * This function consumes a stream of {@link OrderPartsCreated} messages
 * and produces a stream of {@link OrderLineExported} and {@link OrderPartRejected} messages.
 * <p>
 * During the consumption of {@link OrderPartsCreated}, sometimes it is not possible to place
 * this order in fulfillment tools due to carrier not supported by Fulfilment Tools.
 * In this case, a {@link OrderPartRejected} message is produced.
 * Otherwise, a {@link OrderLineExported} message is produced.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExportOrderToFulfillmentTools
    implements Function<Flux<OrderPartsCreated>, Tuple2<Flux<OrderLineExported>, Flux<OrderPartRejected>>> {

    /**
     * Number of subscribers to this flux. Increase this value in case of new subscribers are added.
     * The OrderLineExported binder is 1 subscriber.
     * The OrderPartRejected binder is 1 subscriber.
     */
    private static final int AMOUNT_OF_SUBSCRIBERS = 2;

    private static final Pattern ALLOWED_FULFILLMENT_NODES = Pattern.compile("SHIP_FROM_STORE_[A-Z]{2}");

    private final ValidationService validationService;

    private final FulfillmentToolsService fulfillmentToolsService;

    private final EcommerceEventConverter ecommerceEventConverter;

    private final EcommerceEnrichmentService ecommerceEnrichmentService;

    private final OrderUploaderService orderUploaderService;

    @Override
    public Tuple2<Flux<OrderLineExported>, Flux<OrderPartRejected>> apply(Flux<OrderPartsCreated> orderPartsCreatedFlux) {

        Flux<EnrichedOrderPartsCreated> enrichedOrderPartsCreated = orderPartsCreatedFlux.log("OrderPartsCreated received")
            .doOnNext(validationService::validate)
            .filter(this::isOrderPartShipFromStore)
            .filter(this::isANewOrder)
            .map(ecommerceEnrichmentService::enrichOrderWithProductInformation)
            .doOnNext(enrichIfPossible())
            .publish()
            .autoConnect(AMOUNT_OF_SUBSCRIBERS);

        return Tuples.of(
            enrichedOrderPartsCreated
                .filter(enriched -> !enriched.isImpossibleToPlaceOrder())
                .doOnNext(enriched -> {
                    fulfillmentToolsService.placeAnOrder(enriched.getOrderCreation());
                    orderUploaderService.uploadOrder(enriched.getOrderCreation());
                })
                .map(EnrichedOrderPartsCreated::getOrderPartsCreated)
                .flatMapIterable(ecommerceEventConverter::convert)
                .doOnNext(item -> log.info("OrderLineExported: {}", item))
                .transform(FunctionUtils::onErrorContinue)
                .log("Order Placement"),

            enrichedOrderPartsCreated
                .filter(EnrichedOrderPartsCreated::isImpossibleToPlaceOrder)
                .map(ecommerceEventConverter::rejectOrder)
                .doOnNext(item -> log.info("OrderPartRejected has been produced for Order: {}", item.getOrderId()))
                .transform(FunctionUtils::onErrorContinue)
                .log("Order Rejection")
        );
    }

    private Consumer<EnrichedOrderPartsCreated> enrichIfPossible() {
        return enriched -> {
            try {
                enriched.setOrderCreation(ecommerceEventConverter.convertToOrderCreation(enriched));
            } catch (ImpossibleToPlaceOrderException e) {
                enriched.setImpossibleToPlaceOrder(true);
                log.warn(
                    "Impossible to place order {}. OrderPartRejected will be produced. No support is needed for this order.",
                    enriched.getOrderPartsCreated().getOrderId(), e);
            }
        };
    }

    private boolean isANewOrder(OrderPartsCreated orderPartsCreated) {
        return fulfillmentToolsService.isANewOrder(orderPartsCreated.getOrderId());
    }

    /**
     * The method is used for validating the fulfillment node. Any fulfillment node with SHIP_FROM_STORE_XX will be
     * accepted.
     *
     * @param orderPartsCreated the order parts created message to check
     */
    private boolean isOrderPartShipFromStore(OrderPartsCreated orderPartsCreated) {
        return ALLOWED_FULFILLMENT_NODES.asMatchPredicate().test(orderPartsCreated.getFulfillmentNode());
    }
}
