package com.bestseller.storeorderfulfillmentservice.messaging;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.fulfillmenttoolseventsreceived.FulfillmentToolsEventsReceived;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineAcknowledged.OrderLineAcknowledged;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderLineDispatched.OrderLineDispatched;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartsCancelled.OrderPartsCancelled;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.OrderCancelled;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.Payload;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.dispatched.HandoverJobHandedOver;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.notroutable.RoutingPlanNotRoutable;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.packjob.PackJobCreated;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobAborted;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobCreated;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobPickingFinished;
import com.bestseller.storeorderfulfillmentservice.converter.FulfillmentEventConverter;
import com.bestseller.storeorderfulfillmentservice.converter.OrderDetailsConverter;
import com.bestseller.storeorderfulfillmentservice.service.FulfillmentEventEnrichmentService;
import com.bestseller.storeorderfulfillmentservice.service.ValidationService;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.DocumentService;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.EventUploaderService;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple4;
import reactor.util.function.Tuples;

import java.time.Duration;
import java.time.Instant;
import java.util.function.Function;

/**
 * Message processing pipeline for Fulfillment tools events.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransformFulfillmentToolsEvent
    implements Function<Flux<FulfillmentToolsEventsReceived>,
    Tuple4<Flux<OrderLineAcknowledged>, Flux<OrderLineDispatched>, Flux<OrderPartRejected>, Flux<OrderPartsCancelled>>> {

    private final ValidationService validationService;

    private final FulfillmentEventEnrichmentService enrichmentService;

    private final FulfillmentEventConverter fulfillmentEventConverter;

    private final DocumentService documentService;

    private final OrderDetailsConverter orderDetailsConverter;

    private final EventUploaderService eventUploaderService;

    @Setter
    @Value("${order.reroute.period}")
    private Duration reroutePeriod;

    /**
     * A function that translates a subset of order fulfillment events from Fulfillment Tools to BESTSELLER e-commerce format.
     * The fulfillment events are represented as a heterogeneous stream with order granularity.
     * BESTSELLER events are represented as separate homogeneous streams with order or order line granularity.
     * BESTSELLER events contain extra information for which enrichment is performed.
     *
     * <p>The function is using a triplet as its result type. That's how you output multiple results from a function.
     *
     * <p><strong>Backpressure handling:</strong>
     * Once all 3 outputs are subscribed to (e.g. by Spring Cloud Stream upon application startup), the input is automatically subscribed to.
     *
     * <p><strong>Error handling:</strong>
     * Invalid items are logged and discarded.
     * Errors that would otherwise be emitted by the input (e.g. by Spring Cloud Stream upon deserialization error) are logged and suppressed.
     */
    @Override
    public Tuple4<Flux<OrderLineAcknowledged>, Flux<OrderLineDispatched>, Flux<OrderPartRejected>, Flux<OrderPartsCancelled>> apply(
        Flux<FulfillmentToolsEventsReceived> event) {

        // one time we subscribe ourselves below for the document attachment (that's 1 sub)
        // the user of this method is supposed to subscribe to all output publishers (that's 4 more subs)
        // the merge subscribes to its sources when its result is subscribed to (yet another sub)
        final int subscribers = 7;

        Flux<FulfillmentToolsEventsReceived> uploadedEvents = event
            .log("Fulfillment Tools event")
            .doOnNext(fulfillmentToolsEvent -> {
                validationService.validate(fulfillmentToolsEvent);
                eventUploaderService.uploadEvent(fulfillmentToolsEvent);
            });

        Flux<Payload> payloads = uploadedEvents
            .map(FulfillmentToolsEventsReceived::getPayload)
            .publish().autoConnect(subscribers);

        payloads
            .ofType(PackJobCreated.class)
            .log("PackJobCreated event")
            .map(orderDetailsConverter::convert)
            .doOnNext(enrichmentService::enrichWithFacilityDetails)
            .doOnNext(enrichmentService::enrichWithPrice)
            .doOnNext(documentService::attachDocuments)
            .transform(FunctionUtils::onErrorContinue)
            .subscribe(); // here we subscribe ourselves #1

        return Tuples.of(
            // will be subscribed to by the method's user #2
            payloads
                .ofType(PickJobCreated.class)
                .map(enrichmentService::enrichWithFacility)
                .flatMapIterable(fulfillmentEventConverter::convert)
                .transform(FunctionUtils::onErrorContinue)
                .log("OrderLineAcknowledged event"),

            // will be subscribed by the method's user #3
            payloads
                .ofType(HandoverJobHandedOver.class)
                .map(enrichmentService::enrichWithFacilityAndParcel)
                .flatMapIterable(fulfillmentEventConverter::convert)
                .transform(FunctionUtils::onErrorContinue)
                .log("OrderLineDispatched event"),

            // will be subscribed to by the method's user
            Flux.merge(
                    // will be subscribed to by the merge #4
                    payloads
                        .ofType(RoutingPlanNotRoutable.class)
                        .log("RoutingPlanNotRoutable event")
                        .map(fulfillmentEventConverter::convert),

                    // will be subscribed to by the merge #5
                    payloads
                        .ofType(PickJobAborted.class)
                        .log("PickJobAborted event")
                        .filter(pickJobAborted -> !canReroute(pickJobAborted))
                        .map(enrichmentService::enrichWithFacility)
                        .map(fulfillmentEventConverter::convert),

                    // will be subscribed to by the merge #6
                    payloads
                        .ofType(OrderCancelled.class)
                        .log("OrderCancelled event")
                        .map(fulfillmentEventConverter::convert)
                )
                .transform(FunctionUtils::onErrorContinue)
                .log("OrderPartRejected event"),

            // will be subscribed to by the method's user #7
            payloads
                .ofType(PickJobPickingFinished.class)
                .map(enrichmentService::enrichWithFacility)
                .map(fulfillmentEventConverter::convert)
                .flatMap(Mono::justOrEmpty)
                .transform(FunctionUtils::onErrorContinue)
                .log("OrderPartsCancelled event")
        );
    }

    private boolean canReroute(PickJobAborted pickJobAborted) {
        Instant expiration = pickJobAborted.getOrderDate().plus(reroutePeriod);
        return pickJobAborted.getCreated().isBefore(expiration);
    }
}
