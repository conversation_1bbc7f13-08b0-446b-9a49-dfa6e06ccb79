package com.bestseller.storeorderfulfillmentservice.messaging;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * Base function class contains common methods to functions.
 */
@Slf4j
public final class FunctionUtils {
    private FunctionUtils() {
    }

    /**
     * Handle errors and continue.
     * @param publisher
     * @return flux
     */
    public static <T> Flux<T> onErrorContinue(Flux<T> publisher) {
        // recover from deserialization, validation, enrichment, and other error
        return publisher.onErrorContinue((exception, item) -> log.error("Error processing item {}", item, exception));
    }

}
