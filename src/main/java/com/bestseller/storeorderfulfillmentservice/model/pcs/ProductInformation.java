package com.bestseller.storeorderfulfillmentservice.model.pcs;

import lombok.Builder;

import java.util.List;

/**
 * ProductInformation class to use on graphql queries.
 */
@Builder
public record ProductInformation(String ean, String styleNumber,
                                 String styleOption, String color,
                                 String size, String length, String productName,
                                 String ediStyleName, List<String> imageReferencesUrls, String brandAbbreviation, String brand) {
}
