package com.bestseller.storeorderfulfillmentservice.model.localization;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Class responsible for transfer the translations to the fields on the template invoice file.
 */
@Data
@NoArgsConstructor
@SuppressWarnings("PMD.TooManyFields")
public class Localization {
    private String invoice;
    private String returnLabel;
    private String returnedItems;
    private String shippingAddress;
    private String orderNumber;
    private String orderDate;
    private String shippingDate;
    private String itemsOverview;
    private String deliveryContainsFollowingItems;
    private String item;
    private String itemNumber;
    private String itemPrice;
    private String itemQuantity;
    private String totalPrice;
    private String shippingCost;
    private String totalAmount;
    private String pricesIncludeVat;
    private String thanksForThePurchase;
    private String returnConditions;
    private List<String> returnConditionsItems;
    private String notSatisfiedWithTheProduct;
    private List<String> returnInstructions;
    private String doYouHaveFurtherQuestions;
    private Company company;
    private String currencySign;
}
