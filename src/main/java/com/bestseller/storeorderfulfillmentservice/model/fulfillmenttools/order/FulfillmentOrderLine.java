package com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Order creation request.
 */
@Data
@Builder
@AllArgsConstructor
public class FulfillmentOrderLine {

    private Article article;
    private int quantity;
    private List<String> scannableCodes;
    private BigDecimal shopPrice;
    private List<BrandTag> tags;

}
