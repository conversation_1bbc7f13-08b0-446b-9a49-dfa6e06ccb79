package com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPartRejected.OrderPartRejected;

/**
 * Cancellation reason enum for {@link OrderPartRejected} event.
 */
public enum CancellationReason {
    /**
     * The item is not available or the order is rejected by the stores.
     */
    ITEM_NOT_AVAILABLE
}
