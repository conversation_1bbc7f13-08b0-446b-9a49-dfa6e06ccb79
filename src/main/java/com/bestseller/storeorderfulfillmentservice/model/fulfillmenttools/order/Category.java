package com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order;

import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Locale;

/**
 * Enum regarding the attribute categories.
 */
public enum Category {

    DESCRIPTIVE,
    MISCELLANEOUS;

    /**
     * Get category description.
     * @return description
     */
    @JsonValue
    public String getDescription() {
        return name().toLowerCase(Locale.getDefault());
    }
}
