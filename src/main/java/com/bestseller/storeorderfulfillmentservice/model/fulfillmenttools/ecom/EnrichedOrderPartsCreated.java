package com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.ecom;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.OrderCreation;
import com.bestseller.storeorderfulfillmentservice.model.pcs.ProductInformation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Class containing all objects necessary to order exportation.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnrichedOrderPartsCreated {

    private OrderPartsCreated orderPartsCreated;

    private Map<String, ProductInformation> productsInformation;

    private OrderCreation orderCreation;

    private boolean impossibleToPlaceOrder;
}
