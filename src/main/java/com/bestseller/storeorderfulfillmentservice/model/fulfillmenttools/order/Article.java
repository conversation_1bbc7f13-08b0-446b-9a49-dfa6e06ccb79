package com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Article.
 */
@Data
@Builder
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Article {

    @JsonProperty("tenantArticleId")
    private String ean;

    private String title;

    private String imageUrl;

    private List<Attribute> attributes;

}
