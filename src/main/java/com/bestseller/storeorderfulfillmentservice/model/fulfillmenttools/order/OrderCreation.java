package com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order;

import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.DeliveryPreferences;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * Order creation request.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderCreation {

    private ConsumerInformation consumer;

    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX",
            timezone = "UTC"
    )
    private Instant orderDate;

    private String tenantOrderId;

    private List<FulfillmentOrderLine> orderLineItems;

    private CustomAttributes customAttributes;

    private DeliveryPreferences deliveryPreferences;
}
