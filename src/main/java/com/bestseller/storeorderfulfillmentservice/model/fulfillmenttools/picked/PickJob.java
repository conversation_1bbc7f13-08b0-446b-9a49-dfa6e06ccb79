package com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.picked;

import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.LineItem;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.CustomAttributes;
import lombok.Builder;

import java.util.List;

/**
 * Fulfillment Tools pick job object.
 */
@Builder
public record PickJob(List<LineItem> pickLineItems, CustomAttributes customAttributes) {
}
