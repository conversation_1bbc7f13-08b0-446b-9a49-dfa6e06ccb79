package com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools;

import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.PhoneNumber;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * Address class.
 */
@Data
@SuperBuilder(toBuilder = true)
@NoArgsConstructor
public class Address {

    private String additionalAddressInfo;

    private String city;

    private String country;

    private String province;

    private String houseNumber;

    private List<PhoneNumber> phoneNumbers;

    private String postalCode;

    private String street;

}
