package com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order;

import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.Address;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Consumer address.
 */
@Data
@SuperBuilder(toBuilder = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConsumerAddress extends Address {

    private String firstName;

    private String lastName;

    private String companyName;

    private AddressType addressType;

    /**
     * accepted address type by FFT.
     * */
    public enum AddressType {
        POSTAL_ADDRESS,
        PARCEL_LOCKER,
        INVOICE_ADDRESS
    }

}
