package com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Shipping {

    private List<String> preferredCarriers;

    private List<ServiceOption> preferredCarriersWithProduct;

    @Builder.Default
    private String serviceLevel = "DELIVERY";

}


