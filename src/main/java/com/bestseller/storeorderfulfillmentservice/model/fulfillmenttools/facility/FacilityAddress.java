package com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.facility;

import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.Address;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Facility address.
 */
@Data
@SuperBuilder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public final class FacilityAddress extends Address {
    private String companyName;
}
