package com.bestseller.storeorderfulfillmentservice.model;

import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.dispatched.HandoverJobHandedOver;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.Parcel;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.carrier.Carrier;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.facility.Facility;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.picked.PickJob;

public record EnrichedHandoverJobHandedOver(HandoverJobHandedOver fulfillmentEvent, Facility facility,
                                            Parcel parcel, PickJob pickJob, Carrier carrier) {
}
