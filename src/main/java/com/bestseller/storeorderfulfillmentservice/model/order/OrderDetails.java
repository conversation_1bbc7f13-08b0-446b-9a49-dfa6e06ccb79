package com.bestseller.storeorderfulfillmentservice.model.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Order details.
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderDetails {
    private String processId;
    private StoreDetails storeDetails;
    private CustomerDetails customerDetails;
    private String orderNumber;
    private String shipmentDate;
    private String placementDate;
    private List<OrderLine> orderLines;
    private BigDecimal itemsPrice;
    private BigDecimal shippingPrice;
    private BigDecimal totalPrice;
    private String pickJobReference;
    private String brand;
    private String carrier;
}
