package com.bestseller.storeorderfulfillmentservice.model.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

/**
 * Store Details.
 */
@Data
@RequiredArgsConstructor
@Builder
@AllArgsConstructor
public class StoreDetails {

    private String facilityReference;

    private String locationChainCode;

    private String brand;

    private String city;

    private String street;

    private String country;
}
