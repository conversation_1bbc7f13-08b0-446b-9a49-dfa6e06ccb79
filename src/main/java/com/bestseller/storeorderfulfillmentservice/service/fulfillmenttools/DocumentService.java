package com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools;

import com.bestseller.storeorderfulfillmentservice.exception.DocumentGenerationException;
import com.bestseller.storeorderfulfillmentservice.exception.fft.FulfillmentToolsProcessException;
import com.bestseller.storeorderfulfillmentservice.model.order.OrderDetails;
import com.bestseller.storeorderfulfillmentservice.service.FileRenderingService;
import com.bestseller.storeorderfulfillmentservice.service.S3Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Arrays;

/**
 * Service responsible to handle PackJobCreated events.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentService {
    private final FulfillmentToolsService fulfillmentToolsService;

    private final FileRenderingService fileRenderingService;

    private final S3Service s3Service;

    /**
     * Generating a PDF file by order details, send it to fulfillment tools and save it in S3 bucket.
     *
     * @param orderDetails
     */
    public void attachDocuments(OrderDetails orderDetails) {
        try {
            var mergedDocumentOutputStream = new ByteArrayOutputStream();
            String orderNumber = orderDetails.getOrderNumber();
            String processId = orderDetails.getProcessId();
            log.info("Start generating document for orderNumber={} and processId={}", orderNumber, processId);
            generateDocument(orderDetails, mergedDocumentOutputStream);

            byte[] mergedDocument = mergedDocumentOutputStream.toByteArray();
            fulfillmentToolsService.postDocumentToProcess(
                new ByteArrayInputStream(mergedDocument),
                orderDetails.getProcessId(),
                "invoice-return-label.pdf");

            s3Service.uploadDocument(
                new ByteArrayInputStream(mergedDocument),
                "documents/",
                "%s.pdf".formatted(orderDetails.getOrderNumber()));
        } catch (IOException e) {
            var message = "Failed to sent PDF to order %s - process %s".formatted(orderDetails.getOrderNumber(),
                orderDetails.getProcessId());
            throw new FulfillmentToolsProcessException(message, e);
        }
    }

    /**
     * Generating a PDF file by order details.
     *
     * @param orderDetails
     * @param outputStream
     * @throws IOException
     */
    @SuppressWarnings("checkstyle:IllegalCatch")
    public void generateDocument(OrderDetails orderDetails, OutputStream outputStream) throws IOException {
        String orderNumber = orderDetails.getOrderNumber();
        String processId = orderDetails.getProcessId();

        long startTime = System.currentTimeMillis();

        try (ByteArrayOutputStream invoiceOutputStream = new ByteArrayOutputStream();
             ByteArrayOutputStream returnSlipOutputStream = new ByteArrayOutputStream()) {

            try {
                long invoiceStart = System.currentTimeMillis();
                fileRenderingService.writeInvoice(orderDetails, invoiceOutputStream);
                log.debug("Invoice generated in {} ms for orderNumber={}", System.currentTimeMillis() - invoiceStart, orderNumber);

                long slipStart = System.currentTimeMillis();
                fileRenderingService.writeReturnSlip(orderDetails, returnSlipOutputStream);
                log.debug("Return slip generated in {} ms for orderNumber={}", System.currentTimeMillis() - slipStart, orderNumber);

                long mergeStart = System.currentTimeMillis();
                mergeDocuments(outputStream, invoiceOutputStream, returnSlipOutputStream);
                log.debug("Documents merged in {} ms for orderNumber={}", System.currentTimeMillis() - mergeStart, orderNumber);

                log.info("Successfully completed document generation for orderNumber={} and processId={} in total {} ms",
                    orderNumber, processId, System.currentTimeMillis() - startTime);

            } catch (IOException e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("Error during document generation for orderNumber={} and processId= {} after {} ms", orderNumber, processId, duration, e);
                throw e;

            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("Unexpected error during document generation for orderNumber={} and processId={} after {} ms",
                    orderNumber, processId, duration, e);
                throw new DocumentGenerationException("Unexpected error during document generation for orderNumber= " + orderNumber, e);
            }
        }
    }

    private void mergeDocuments(OutputStream mergedDestination,
                                ByteArrayOutputStream... sourceOutputStreams)
        throws IOException {
        PDFMergerUtility pdfMerger = new PDFMergerUtility();
        pdfMerger.setDestinationStream(mergedDestination);

        pdfMerger.addSources(Arrays.stream(sourceOutputStreams)
            .map(source -> (InputStream) new ByteArrayInputStream(source.toByteArray()))
            .toList());
        pdfMerger.mergeDocuments(null);
    }
}
