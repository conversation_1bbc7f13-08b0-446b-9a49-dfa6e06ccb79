package com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools;

import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.OrderCreation;
import com.bestseller.storeorderfulfillmentservice.service.S3Service;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Service for uploader Order Creation payloads to S3.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderUploaderService {

    private static final String S3_DIRECTORY = "orders/";
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss");

    private final S3Service s3Service;
    private final ObjectMapper objectMapper;

    /**
     * Uploads an OrderCreation payload to the S3 bucket.
     *
     * @param orderCreation The OrderCreation payload to upload
     */
    public void uploadOrder(OrderCreation orderCreation) {
        try {
            String orderId = orderCreation.getTenantOrderId();
            String timestamp = LocalDateTime.now().format(DATE_TIME_FORMATTER);

            String fileName = "%s_%s.json".formatted(
                orderId,
                timestamp
            );

            byte[] jsonContent = objectMapper.writeValueAsBytes(orderCreation);
            s3Service.uploadJsonContent(jsonContent, S3_DIRECTORY, fileName);

            log.info("Successfully uploaded order creation payload for order {} to S3: {}", orderId, fileName);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize OrderCreation to JSON", e);
        }
    }
}
