package com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.fulfillmenttoolseventsreceived.FulfillmentToolsEventsReceived;
import com.bestseller.storeorderfulfillmentservice.service.S3Service;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * Service for uploading Fulfillment Tools events to S3.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EventUploaderService {

    private static final String S3_DIRECTORY = "fft-events/";
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss");

    private final S3Service s3Service;
    private final ObjectMapper objectMapper;

    /**
     * Uploads a FulfillmentToolsEventsReceived message to the S3 bucket.
     *
     * @param event The FulfillmentToolsEventsReceived message to upload
     */
    public void uploadEvent(FulfillmentToolsEventsReceived event) {
        try {
            String eventType = event.getEvent().toLowerCase(Locale.ROOT);
            String orderId = event.getPayload().getTenantOrderId();
            String timestamp = LocalDateTime.now().format(DATE_TIME_FORMATTER);

            String fileName = "%s_%s_%s.json".formatted(
                orderId,
                eventType,
                timestamp
            );

            byte[] jsonContent = objectMapper.writeValueAsBytes(event);
            s3Service.uploadJsonContent(jsonContent, S3_DIRECTORY, fileName);

            log.info("Successfully uploaded {} event for order {} to S3: {}", eventType, orderId, fileName);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize FulfillmentToolsEventsReceived to JSON", e);
        }
    }
}
