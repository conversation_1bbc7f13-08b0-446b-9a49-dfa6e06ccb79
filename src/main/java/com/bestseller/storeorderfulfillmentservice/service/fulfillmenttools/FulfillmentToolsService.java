package com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools;

import com.bestseller.fulfillmenttoolsclient.service.FulfillmentToolsAuthService;
import com.bestseller.storeorderfulfillmentservice.exception.fft.OrderCreationException;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.Parcel;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.carrier.Carrier;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.facility.Facility;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.OrderCreation;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.OrderResponseItem;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.order.OrdersResponse;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.picked.PickJob;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.process.ProcessFile;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.process.ProcessRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.util.retry.Retry;
import reactor.util.retry.RetryBackoffSpec;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.Base64;
import java.util.Map;
import java.util.function.Consumer;

/**
 * Service that handles the Fulfillment tools order events.
 */
@Slf4j
@Service
@Validated
/**
 * Order event service that is responsible for routing a Fulfillment tools event to its relevant service.
 */
public class FulfillmentToolsService {

    private static final int MAX_ATTEMPTS = 10;

    private static final String API_PROCESSES = "/api/processes/";

    private final FulfillmentToolsAuthService fulfillmentToolsAuthService;
    private final WebClient fulfillmentToolsClient;

    private final Duration minBackoff;

    /**
     * Constructor.
     *
     * @param fulfillmentToolsAuthService
     * @param fulfillmentToolsClient
     * @param minBackoff
     */
    public FulfillmentToolsService(FulfillmentToolsAuthService fulfillmentToolsAuthService,
                                   WebClient fulfillmentToolsClient,
                                   @Value("${fft.webclient.backoff.delay}") final Duration minBackoff) {
        this.fulfillmentToolsAuthService = fulfillmentToolsAuthService;
        this.fulfillmentToolsClient = fulfillmentToolsClient;
        this.minBackoff = minBackoff;
    }

    /**
     * Finds the facility based on the facility id.
     *
     * @param facilityId
     * @return Facility
     */
    @Cacheable
    public Facility getFacility(@NotNull String facilityId) {
        return get("/api/facilities/{facility ID}", Map.of("facility ID", facilityId), Facility.class);
    }

    /**
     * Finds the parcel based on the parcel id.
     *
     * @param parcelId
     * @return Parcel
     */
    public Parcel getParcel(@NotNull String parcelId) {
        return get("/api/parcels/{parcel ID}", Map.of("parcel ID", parcelId), Parcel.class);
    }

    /**
     * Finds the pick job based on the facility id.
     *
     * @param pickJobId
     * @return PickJob
     */
    public PickJob getPickJob(@NotNull String pickJobId) {
        return get("/api/pickjobs/{pick job ID}", Map.of("pick job ID", pickJobId), PickJob.class);
    }

    /**
     * Finds the carrier based on the carrier id.
     *
     * @param carrierId
     * @return Carrier
     */
    @Cacheable
    public Carrier getCarrier(@NotNull String carrierId) {
        return get("/api/carriers/{carrier id}", Map.of("carrier id", carrierId), Carrier.class);
    }

    /**
     * Attach document to a FFT process.
     *
     * @param content
     * @param processId
     */
    public void postDocumentToProcess(InputStream content, String processId, String fileName) throws IOException {
        log.info("Publishing document to process id {}", processId);
        long startTime = System.currentTimeMillis();
        var processRequest = ProcessRequest.builder()
            .type("PDF")
            .section("PACKJOB")
            .file(ProcessFile.builder()
                .name(fileName)
                .content(Base64.getEncoder().encodeToString(content.readAllBytes()))
                .build())
            .build();
        fulfillmentToolsClient.post()
            .uri(uriBuilder -> uriBuilder
                .path(API_PROCESSES)
                .path(processId)
                .path("/documents")
                .build()
            ).headers(this.addToken())
            .bodyValue(processRequest)
            .retrieve()
            .bodyToMono(Void.class)
            .retryWhen(backoffRetry())
            .doOnSuccess(response -> log.info("Document {} was successfully attached to process id {} in {} ms", fileName, processId,
                System.currentTimeMillis() - startTime))
            .doOnError(error -> log.error("Failed to attach document {} to process id {} : {}", fileName, processId, error.getMessage()))
            .block();
    }

    /**
     * Check whether the order was already placed or not.
     *
     * @param orderId
     * @return if the order was not placed previously.
     */
    // DO not cache this method. Otherwise, none duplicated order will be rejected.
    public boolean isANewOrder(String orderId) {
        var orders = getOrdersByTenantId(orderId).orders();
        if (!orders.isEmpty()) {
            log.warn("Order {} already exists in Fulfillment Tools with ID {}",
                orderId,
                orders.stream()
                    .map(OrderResponseItem::id)
                    .toList());
        }
        return orders.isEmpty();
    }

    /**
     * Place an order to FFT.
     *
     * @param orderCreation
     */
    public void placeAnOrder(OrderCreation orderCreation) {
        fulfillmentToolsClient.post()
            .uri(uriBuilder -> uriBuilder
                .path("/api/orders/")
                .build()
            ).headers(this.addToken())
            .bodyValue(orderCreation)
            .retrieve()
            .bodyToMono(Void.class)
            .retryWhen(backoffRetry())
            .onErrorMap(e -> new OrderCreationException(orderCreation, e))
            .block();
    }

    // DO not cache this request
    private OrdersResponse getOrdersByTenantId(String tenantOrderId) {
        log.info("Finding orders by tenant order id {}", tenantOrderId);
        return get("/api/orders/?tenantOrderId={tenant order id}",
            Map.of("tenant order id", tenantOrderId),
            OrdersResponse.class);
    }

    private <T> T get(String uriTemplate, Map<String, String> uriVariables, Class<T> responseType) {
        log.info("Retrieving {}: {}", responseType.getSimpleName(), uriVariables);
        return fulfillmentToolsClient.get()
            .uri(uriTemplate, uriVariables)
            .headers(this.addToken())
            .retrieve()
            .bodyToMono(responseType)
            .retryWhen(backoffRetry())
            .doOnSuccess(response -> log.info("Retrieved {}: {}", response, uriVariables))
            .block();
    }

    private RetryBackoffSpec backoffRetry() {
        return Retry.backoff(MAX_ATTEMPTS, minBackoff)
            .filter(this::isRetryableException);
    }

    private Consumer<HttpHeaders> addToken() {
        return httpHeaders -> httpHeaders.setBearerAuth(fulfillmentToolsAuthService.getToken());
    }

    // All exceptions excluding 4xx errors are retryable.
    private boolean isRetryableException(Throwable throwable) {
        return !(throwable instanceof WebClientResponseException exception && exception.getStatusCode().is4xxClientError());
    }
}
