package com.bestseller.storeorderfulfillmentservice.service;

import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.ObjectMetadata;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.file.Paths;

/**
 * Service for Amazon S3 operations.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class S3Service {

    private final AmazonS3 amazonS3;

    @Value("${s3.bucket}")
    private String bucket;

    /**
     * Upload file to s3 bucket.
     *
     * @param inputStream
     * @param remoteDirectory
     * @param fileName
     */
    public void uploadDocument(final ByteArrayInputStream inputStream, String remoteDirectory, String fileName) throws IOException {
        String fullPath = Paths.get(remoteDirectory, fileName).toString();
        var objectMetadata = new ObjectMetadata();
        objectMetadata.setContentLength(inputStream.available());
        amazonS3.putObject(bucket, fullPath, inputStream, objectMetadata);
    }

    /**
     * Upload JSON content to s3 bucket. Any exception during the upload will be logged, but not thrown
     * to avoid breaking the application flow.
     *
     * @param jsonContent The JSON content as a byte array
     * @param remoteDirectory The directory in S3 where the file will be stored
     * @param fileName The name of the file to be created
     */
    public void uploadJsonContent(final byte[] jsonContent, String remoteDirectory, String fileName) {
        String fullPath = Paths.get(remoteDirectory, fileName).toString();
        ByteArrayInputStream inputStream = new ByteArrayInputStream(jsonContent);

        var objectMetadata = new ObjectMetadata();
        objectMetadata.setContentLength(jsonContent.length);
        objectMetadata.setContentType(MediaType.APPLICATION_JSON_VALUE);

        try {
            amazonS3.putObject(bucket, fullPath, inputStream, objectMetadata);
            log.info("Successfully uploaded JSON file to S3: {}", fullPath);
        } catch (AmazonS3Exception e) {
            log.error("S3 service error while uploading JSON file {}: {}", fullPath, e.getMessage());
        } catch (SdkClientException e) {
            log.error("AWS SDK client error while uploading JSON file {}: {}", fullPath, e.getMessage());
        }
    }
}
