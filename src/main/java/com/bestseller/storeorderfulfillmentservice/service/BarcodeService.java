package com.bestseller.storeorderfulfillmentservice.service;

import com.bestseller.storeorderfulfillmentservice.exception.BarcodeGenerationException;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.oned.Code128Writer;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Base64.Encoder;
import java.util.HashMap;
import java.util.Map;

@Service
public class BarcodeService {

    private static final int BARCODE_WIDTH = 300;
    private static final int BARCODE_HEIGHT = 50;

    private static final Map<EncodeHintType, Object> HINTS = new HashMap<>();
    private static final String PNG = "PNG";
    private static final Encoder BASE64_ENCODER = Base64.getEncoder();
    private static final String DATA_IMAGE_PNG_BASE_64 = "data:image/png;base64,";

    public BarcodeService() {
        HINTS.put(EncodeHintType.MARGIN, 0);
    }

    public String generateCode128Barcode(String orderId) {
        // Generate barcode image
        BitMatrix bitMatrix = new Code128Writer()
            .encode(orderId, BarcodeFormat.CODE_128, BARCODE_WIDTH, BARCODE_HEIGHT, HINTS);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            MatrixToImageWriter.writeToStream(bitMatrix, PNG, outputStream);
        } catch (IOException e) {
            throw new BarcodeGenerationException(orderId, e);
        }

        // Convert to Base64 string
        byte[] barcodeBytes = outputStream.toByteArray();
        String barcodeBase64 = BASE64_ENCODER.encodeToString(barcodeBytes);
        return DATA_IMAGE_PNG_BASE_64 + barcodeBase64;
    }

}
