package com.bestseller.storeorderfulfillmentservice.service;

import com.bestseller.storeorderfulfillmentservice.configuration.brand.BrandPropertiesConfiguration;
import com.bestseller.storeorderfulfillmentservice.configuration.localization.LocalizationConfig;
import com.bestseller.storeorderfulfillmentservice.exception.HtmlToPdfParsingException;
import com.bestseller.storeorderfulfillmentservice.model.order.OrderDetails;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.openhtmltopdf.util.XRRuntimeException;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.Writer;
import java.util.Map;

/**
 * Service for rendering file.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@SuppressWarnings("PMD.UseProperClassLoader")
public class FileRenderingService {
    private static final String TEMPLATE_DATA_PARAM_NAME = "data";
    private static final String LOCALIZED_PARAM_NAME = "localized";
    private static final String BRAND_CONFIG_PARAM_NAME = "brand";

    private final Configuration templateConfiguration;
    private final LocalizationConfig localizationConfig;
    private final BrandPropertiesConfiguration brandPropertiesConfiguration;
    private final BarcodeService barcodeService;
    private final FontService fontService;

    /**
     * Generate invoice PDF file.
     *
     * @param orderDetails order detail
     * @param outputStream result output
     */
    public void writeInvoice(OrderDetails orderDetails, OutputStream outputStream) {
        writePdfForTemplate("invoice.ftlh", orderDetails, outputStream);
    }

    /**
     * Generate return slip PDF file.
     *
     * @param orderDetails order detail
     * @param outputStream result output
     */
    public void writeReturnSlip(OrderDetails orderDetails, OutputStream outputStream) {
        writePdfForTemplate("return-slip.ftlh", orderDetails, outputStream);
    }

    private void writePdfForTemplate(String templateFile, OrderDetails orderDetails, OutputStream outputStream) {
        Map<String, Object> localizedTemplateParams = getLocalizedTemplateParams(
            orderDetails,
            orderDetails.getCustomerDetails().countryCode(),
            orderDetails.getBrand(),
            orderDetails.getOrderNumber());

        writePdf(templateFile, localizedTemplateParams, outputStream);
    }

    private void writePdf(String htmlTemplateFileName, Map<String, Object> templateParams, OutputStream outputStream) {
        try {
            PdfRendererBuilder builder = new PdfRendererBuilder();
            builder.withHtmlContent(renderHtml(htmlTemplateFileName, templateParams),
                Thread.currentThread().getContextClassLoader().getResource("template").toString());

            fontService.defineFonts(builder);
            builder.useFastMode();
            builder.toStream(outputStream);
            builder.run();
        } catch (XRRuntimeException | TemplateException | IOException e) {
            throw new HtmlToPdfParsingException("Error on generating PDF from template %s for data %s"
                .formatted(htmlTemplateFileName, templateParams), e);
        }
    }

    private String renderHtml(String htmlTemplateFileName, Map<String, Object> templateParams) throws TemplateException, IOException {
        try (Writer html = new StringWriter()) {
            Template template = templateConfiguration.getTemplate(htmlTemplateFileName);
            template.process(templateParams, html);
            return html.toString();
        }
    }

    private Map<String, Object> getLocalizedTemplateParams(Object data, String country, String brand, String orderId) {

        return Map.of(
            TEMPLATE_DATA_PARAM_NAME, data,
            LOCALIZED_PARAM_NAME, localizationConfig.getLocalizationByCountry(country),
            BRAND_CONFIG_PARAM_NAME, brandPropertiesConfiguration.getBrandProperty(brand),
            "barcodeImage", barcodeService.generateCode128Barcode(orderId));
    }
}
