package com.bestseller.storeorderfulfillmentservice.service.pcs;

import com.bestseller.storeorderfulfillmentservice.exception.pcs.PcsValidationException;
import com.bestseller.storeorderfulfillmentservice.model.pcs.ProductInformation;
import datadog.trace.api.Trace;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service to getProductInformation product information with Product Catalog Service.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProductInformationService {

    private static final Locale LOCALE = new Locale("en", "NL");

    private final GraphQLQueryService pcsGraphqlService;

    /**
     * Query product information the catalog.
     *
     * @param eans EANs to fetch information
     * @return product information by EAN
     */
    @Trace
    public Map<String, ProductInformation> getProductInformation(final Set<String> eans) {
        log.info("PCS: Trying to getProductInformation EANs {}", eans);

        String query = """
                query get_eans_sofs($EANs: [String!]!, $loc: String!) {
                  eans(eans: $EANs, locale: $loc) {
                    ean,
                    styleNumber,
                    styleOption,
                    color,
                    size,
                    length,
                    productName,
                    ediStyleName,
                    imageReferencesUrls,
                    brandAbbreviation,
                    brand
                  }
                }""";
        var variables = Map.of(
                "EANs", eans,
                "loc", LOCALE.toLanguageTag()
        );

        List<ProductInformation> productsInformation =
                pcsGraphqlService.executeQuery(query, variables, "eans", new ParameterizedTypeReference<>() { });

        log.debug("PCS: ProductInformation response: {}", productsInformation);

        if (!responseContainsAllOrderedEans(eans, productsInformation)) {
            throw new PcsValidationException("PCS response doesn't contain all ordered eans: "
                + "requested eans=%s, received eans=%s".formatted(eans,
                    productsInformation.stream().map(ProductInformation::ean).collect(Collectors.toSet())));
        }

        return productsInformation.stream()
                .collect(Collectors.toMap(ProductInformation::ean, Function.identity()));
    }

    private boolean responseContainsAllOrderedEans(Set<String> eans, List<ProductInformation> productsInformation) {
        Set<String> receivedEans =
                productsInformation.stream()
                        .map(ProductInformation::ean)
                        .collect(Collectors.toSet());

        return receivedEans.containsAll(eans);
    }
}
