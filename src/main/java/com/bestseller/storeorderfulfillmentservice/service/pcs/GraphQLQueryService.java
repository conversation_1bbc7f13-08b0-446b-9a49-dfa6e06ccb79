package com.bestseller.storeorderfulfillmentservice.service.pcs;

import com.bestseller.storeorderfulfillmentservice.exception.pcs.PcsRequestException;
import datadog.trace.api.Trace;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Objects;

/**
 * Service to execute GraphQL queries.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GraphQLQueryService {

    private static final long RETRIES = 3;

    private final WebClient pcsClient;

    /**
     * Method to execute graphql queries.
     *
     * @param query query to execute.
     * @param clazz class of the return type.
     * @param <T>   type of the return class.
     * @return Graphql response parsed into desired class.
     */
    @Trace
    public <T> T executeQuery(String query, Map<String, Object> variables, String resolver,
                              ParameterizedTypeReference<GraphQLResponseWrapper<T>> clazz) {
        var params = new GraphqlRequest(query, variables);
        return pcsClient.post()
                .body(Mono.just(params), new ParameterizedTypeReference<>() {
                })
                .retrieve()
                .onStatus(
                        HttpStatus::isError,
                        response ->
                                response.bodyToMono(String.class)
                                        .defaultIfEmpty("No response body was returned by PCS")
                                        .map(body -> new PcsRequestException("""
                                                PCS responded with {}
                                                Query:
                                                {}
                                                Variables:
                                                {}
                                                Response:
                                                {}
                                                """.formatted(response.statusCode(), query, params, body)))

                )
                .bodyToMono(clazz)
                .filter(Objects::nonNull)
                .flatMap(this::failOnErrorPresent)
                .map(GraphQLResponseWrapper::getData)
                .map(response -> response.get(resolver))
                .retry(RETRIES)
                .block();
    }

    private <T> Mono<GraphQLResponseWrapper<T>> failOnErrorPresent(GraphQLResponseWrapper<T> responseWrapper) {
        if (!CollectionUtils.isEmpty(responseWrapper.getErrors())) {
            return Mono.error(new PcsRequestException("Response has errors: " + responseWrapper.getErrors()));
        }
        return Mono.just(responseWrapper);
    }

    record GraphqlRequest(String query, Map<String, Object> variables) {
    }
}
