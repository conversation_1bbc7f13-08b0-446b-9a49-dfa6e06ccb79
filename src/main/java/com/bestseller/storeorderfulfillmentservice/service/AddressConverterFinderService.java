package com.bestseller.storeorderfulfillmentservice.service;

import com.bestseller.storeorderfulfillmentservice.converter.address.AddressService;
import com.bestseller.storeorderfulfillmentservice.model.dto.DeliveryOptionQuery;

import java.util.HashMap;
import java.util.Optional;

@SuppressWarnings("IllegalType")
public class AddressConverterFinderService extends HashMap<String, AddressService> {
    private static final String ANY = "*";

    public AddressService getAddressService(DeliveryOptionQuery deliveryOptionQuery) {
        String identifier = getIdentifier(deliveryOptionQuery.country(),
            deliveryOptionQuery.carrier(),
            deliveryOptionQuery.deliveryType().name());

        if (containsKey(identifier)) {
            return get(identifier);
        }

        String countryOnlyKey = getIdentifier(deliveryOptionQuery.country(), ANY, ANY);
        return Optional.ofNullable(get(countryOnlyKey))
            .orElseThrow(() -> new IllegalArgumentException("No address service found for " + countryOnlyKey));
    }

    public static String getIdentifier(String countryCode, String carrier, String deliveryType) {
        return String.format("%s_%s_%s", countryCode, carrier, deliveryType);
    }
}
