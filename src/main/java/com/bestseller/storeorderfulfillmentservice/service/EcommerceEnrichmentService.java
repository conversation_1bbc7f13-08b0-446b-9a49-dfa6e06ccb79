package com.bestseller.storeorderfulfillmentservice.service;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPart;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.order.orderPartsCreated.OrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.ecom.EnrichedOrderPartsCreated;
import com.bestseller.storeorderfulfillmentservice.model.pcs.ProductInformation;
import com.bestseller.storeorderfulfillmentservice.service.pcs.ProductInformationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service responsible for adding extra e-commerce information.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class EcommerceEnrichmentService {

    private final ProductInformationService productInformationService;

    /**
     * Create enriched order parts created containing also product information.
     *
     * @param orderPartsCreated
     * @return enriched order part created
     */
    public EnrichedOrderPartsCreated enrichOrderWithProductInformation(OrderPartsCreated orderPartsCreated) {
        Map<String, ProductInformation> productsInformation = productInformationService.getProductInformation(orderPartsCreated
                .getOrderParts()
                .stream()
                .map(OrderPart::getOrderLines)
                .flatMap(Collection::stream)
                .map(orderLine -> orderLine.getEan())
                .collect(Collectors.toSet()));
        return EnrichedOrderPartsCreated.builder()
                .orderPartsCreated(orderPartsCreated)
                .productsInformation(productsInformation)
                .build();
    }
}
