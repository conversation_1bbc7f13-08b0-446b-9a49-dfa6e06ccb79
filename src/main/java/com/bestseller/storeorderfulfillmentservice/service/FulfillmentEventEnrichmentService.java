package com.bestseller.storeorderfulfillmentservice.service;

import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.dispatched.HandoverJobHandedOver;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobAborted;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobCreated;
import com.bestseller.interfacecontractannotator.model.fulfillmenttoolseventsreceived.picked.PickJobPickingFinished;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedHandoverJobHandedOver;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedPickJobAborted;
import com.bestseller.storeorderfulfillmentservice.model.EnrichedPickJobCreated;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.Parcel;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.carrier.Carrier;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.facility.Facility;
import com.bestseller.storeorderfulfillmentservice.model.fulfillmenttools.picked.PickJob;
import com.bestseller.storeorderfulfillmentservice.model.order.OrderDetails;
import com.bestseller.storeorderfulfillmentservice.model.pcs.EnrichedPickJobPickingFinished;
import com.bestseller.storeorderfulfillmentservice.service.fulfillmenttools.FulfillmentToolsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Service responsible for adding extra information to order fulfillment events.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class FulfillmentEventEnrichmentService {

    private final FulfillmentToolsService fulfillmentToolsService;

    /**
     * Look up store information from Fulfillment Tools API.
     *
     * @return fulfillment event enriched with store information.
     */
    public EnrichedPickJobCreated enrichWithFacility(PickJobCreated pickJobCreated) {
        Facility facility = fulfillmentToolsService.getFacility(pickJobCreated.getFacilityRef());

        return new EnrichedPickJobCreated(pickJobCreated, facility);
    }

    /**
     * Look up store information from Fulfillment Tools API.
     *
     * @return fulfillment event enriched with store information.
     */
    public EnrichedPickJobPickingFinished enrichWithFacility(PickJobPickingFinished pickJobPickingFinished) {
        Facility facility = fulfillmentToolsService.getFacility(pickJobPickingFinished.getFacilityRef());

        return new EnrichedPickJobPickingFinished(pickJobPickingFinished, facility);
    }

    /**
     * Look up store information from Fulfillment Tools API.
     *
     * @return fulfillment event enriched with store information.
     */
    public EnrichedPickJobAborted enrichWithFacility(PickJobAborted pickJobAborted) {
        Facility facility = fulfillmentToolsService.getFacility(pickJobAborted.getFacilityRef());

        return new EnrichedPickJobAborted(pickJobAborted, facility);
    }

    /**
     * Look up store and shipment information from Fulfillment Tools API.
     *
     * @return fulfillment event enriched with store and shipment information.
     */
    public EnrichedHandoverJobHandedOver enrichWithFacilityAndParcel(HandoverJobHandedOver handoverJobHandedOver) {
        Facility facility = fulfillmentToolsService.getFacility(handoverJobHandedOver.getFacilityRef());
        Parcel parcel = fulfillmentToolsService.getParcel(handoverJobHandedOver.getParcelRef());
        PickJob pickJob = fulfillmentToolsService.getPickJob(handoverJobHandedOver.getPickJobRef());
        Carrier carrier = fulfillmentToolsService.getCarrier(handoverJobHandedOver.getCarrierRef());

        return new EnrichedHandoverJobHandedOver(handoverJobHandedOver, facility, parcel, pickJob, carrier);
    }

    /**
     * Look up store information from Fulfillment Tools API and enrich order details with store information.
     */
    public void enrichWithFacilityDetails(OrderDetails orderDetails) {
        Facility facility = fulfillmentToolsService.getFacility(orderDetails.getStoreDetails().getFacilityReference());
        orderDetails.getStoreDetails().setLocationChainCode(facility.tenantFacilityId());
        orderDetails.getStoreDetails().setStreet(facility.address().getStreet());
        orderDetails.getStoreDetails().setCountry(facility.address().getCountry());
        orderDetails.getStoreDetails().setBrand(facility.address().getCompanyName());
        orderDetails.getStoreDetails().setCity(facility.address().getCity());
    }

    /**
     * Look up the picked items in Fulfillment Tools API and define the total price.
     */
    public void enrichWithPrice(OrderDetails orderDetails) {
        PickJob pickJob = fulfillmentToolsService.getPickJob(orderDetails.getPickJobReference());
        orderDetails.setShippingPrice(pickJob.customAttributes().shipmentCost());
        orderDetails.setTotalPrice(orderDetails.getShippingPrice().add(orderDetails.getItemsPrice()));
    }
}
