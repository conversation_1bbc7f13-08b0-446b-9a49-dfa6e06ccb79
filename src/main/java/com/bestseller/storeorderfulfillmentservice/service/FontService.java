package com.bestseller.storeorderfulfillmentservice.service;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;

@Service
@Slf4j
public class FontService {

    /**
     * Configures fonts for the PDF renderer.
     * Uses Noto Serif as the primary font, with graceful fallback if the font cannot be loaded.
     *
     * @param builder The PDF renderer builder to configure
     */
    public void defineFonts(PdfRendererBuilder builder) {
        // Load font resource as byte array to avoid stream closure issues
        var fontResource = Thread.currentThread().getContextClassLoader().getResource("template/fonts/NotoSerif-Regular.ttf");
        if (fontResource != null) {
            try (var inputStream = fontResource.openStream()) {
                byte[] fontBytes = inputStream.readAllBytes();
                builder.useFont(() -> new ByteArrayInputStream(fontBytes), "Noto Serif");
                log.info("Successfully loaded Noto Serif font");
            } catch (IOException e) {
                log.warn("Failed to load Noto Serif font: {}. Using default font instead.", e.getMessage());
            }
        } else {
            log.warn("Noto Serif font not found in classpath at: template/fonts/NotoSerif-Regular.ttf. Using default font instead.");
        }
    }
}
