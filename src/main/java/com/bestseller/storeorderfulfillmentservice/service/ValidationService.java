package com.bestseller.storeorderfulfillmentservice.service;

import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * Just a validation service.
 */
@Service
@Validated
public class ValidationService {
    /**
     * Validates a message.
     *
     * @param message message to validate
     */
    public void validate(@NotNull @Valid Object message) {

    }
}
