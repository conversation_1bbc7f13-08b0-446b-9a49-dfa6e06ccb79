package com.bestseller.storeorderfulfillmentservice.utils;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.Clock;
import java.time.Instant;
import java.time.ZonedDateTime;

@Component
@RequiredArgsConstructor
public class DateUtils {

    private final Clock clock;

    public ZonedDateTime asZonedDateTime(Instant instant) {
        return ZonedDateTime.ofInstant(instant, clock.getZone());
    }

}
