buildscript {
    dependencies {
        classpath "org.springframework.security:spring-security-core:5.7.5"
    }
}

plugins {
    id 'java'
    id 'org.springframework.boot' version '2.7.7'
    id 'io.spring.dependency-management' version '1.1.0'
    id "io.freefair.lombok" version "6.5.1"
    id "jacoco"
    id "checkstyle"
    id "pmd"
}

apply plugin: "jacoco"
apply plugin: "checkstyle"
apply plugin: "pmd"

group = 'com.bestseller'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '17'

repositories {
    mavenLocal()

    mavenCentral {
        content {
            excludeGroup "com.bestseller"
        }
    }

    maven {
        url = "https://maven.pkg.github.com/bestseller-ecom/bse-commons"
        credentials {
            username ""
            password githubAccessToken
        }
        content {
            includeGroup "com.bestseller"
        }
    }
}

configurations {

    integrationTestImplementation.extendsFrom testImplementation

    compileOnly {
        extendsFrom annotationProcessor
    }
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:2021.0.0"
    }
}

dependencies {
    implementation "org.springframework.cloud:spring-cloud-starter-stream-kafka"
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation "org.springframework.boot:spring-boot-starter-webflux"
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation "org.springframework.boot:spring-boot-starter-security"

    // AWS S3
    def awsSdkVersion = "1.12.339"
    implementation "com.amazonaws:aws-java-sdk-core:${awsSdkVersion}"
    implementation "com.amazonaws:aws-java-sdk-s3:${awsSdkVersion}"

    // Swagger
    implementation 'org.springdoc:springdoc-openapi-ui:1.6.14'

    implementation "com.bestseller:bse-interface-contracts:5.2.9"

    // PDF Generation - OpenHTML
    implementation 'org.freemarker:freemarker:2.3.31'

    def openhtmltopdfVersion = '1.0.10'
    implementation "com.openhtmltopdf:openhtmltopdf-core:${openhtmltopdfVersion}"
    implementation "com.openhtmltopdf:openhtmltopdf-pdfbox:${openhtmltopdfVersion}"
    implementation "com.openhtmltopdf:openhtmltopdf-slf4j:${openhtmltopdfVersion}"

    // Datadog
    def micrometerVersion = '1.9.5'
    implementation "io.micrometer:micrometer-core:${micrometerVersion}"
    implementation "io.micrometer:micrometer-registry-datadog:${micrometerVersion}"

    implementation "org.springframework.boot:spring-boot-starter-actuator"
    implementation "net.logstash.logback:logstash-logback-encoder:7.2"
    implementation 'org.json:json:20231013'

    implementation 'com.neovisionaries:nv-i18n:1.29'

    implementation "com.fasterxml.jackson.core:jackson-annotations:2.14.2"


    def zxingVersion = '3.4.1'
    implementation "com.google.zxing:core:${zxingVersion}"
    implementation "com.google.zxing:javase:${zxingVersion}"

    def extendedidCommonsVersion = '4.0.2'
    implementation "com.bestseller:fulfillmenttools-client:${extendedidCommonsVersion}"

    def ddVersion = '1.0.1'
    implementation "com.datadoghq:dd-trace-api:${ddVersion}"
    runtimeOnly "com.datadoghq:dd-java-agent:${ddVersion}"
    implementation 'org.aspectj:aspectjrt:1.9.9.1'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'com.github.tomakehurst:wiremock-jre8:2.35.1'
    testImplementation "org.springframework.security:spring-security-test"
    testImplementation 'org.mockito:mockito-core:3.3.3'
    testImplementation 'org.awaitility:awaitility:4.2.0'
    testImplementation "com.bestseller:test-utils:${extendedidCommonsVersion}"
    testImplementation 'org.mockito:mockito-inline'

    developmentOnly 'org.springframework.boot:spring-boot-devtools'
}

sourceSets {
    main {
        java {
            srcDirs += "${projectDir}/generated/src/main/java"
        }
    }

    integrationTest {
        java {
            srcDir "src/integration-test/java"
        }

        resources {
            srcDir "src/integration-test/resources"
        }

        compileClasspath += sourceSets.main.output + sourceSets.test.output
        runtimeClasspath += sourceSets.main.output + sourceSets.test.output
    }
}

check {
    finalizedBy jacocoTestCoverageVerification, jacocoTestReport
}

def excludedFiles = ["**/configuration/**/*.class",
                     "**/Application.class",
                     "**/generated/**/*.class",
                     "**/model/**/*.class",
                     "**/service/ValidationService.class",
                     "**/service/BarcodeService.class",
                     "**/service/FontService.class",
                     "**/*Utils.class",
                     "**/exception/*.class"
]
jacoco {
    toolVersion = "0.8.7"
}

jacocoTestReport {
    doFirst {
        classDirectories.files.collect {
            fileTree(dir: it, exclude: excludedFiles)
        }
    }

    reports {
        xml.required = false
        html.required = true
        csv.required = true
    }

    afterEvaluate {
        getClassDirectories().setFrom(classDirectories.files.collect {
            fileTree(dir: it, exclude: excludedFiles)
        })
    }
}

jacocoTestCoverageVerification {
    afterEvaluate {
        getClassDirectories().setFrom(classDirectories.files.collect {
            fileTree(dir: it, exclude: excludedFiles)
        })
    }

    violationRules {
        rule {
            element = "CLASS"
            limit {
                counter = "LINE"
                value = "COVEREDRATIO"
                minimum = 1.000000
            }
            limit {
                counter = "BRANCH"
                value = "COVEREDRATIO"
                minimum = 1.000000
            }
        }
    }
}

checkstyle {
    toolVersion = "10.3.4"
    sourceSets = [sourceSets.main, sourceSets.test, sourceSets.integrationTest]
}

pmd {
    ruleSets = []
    consoleOutput = true
    toolVersion = "6.55.0"
    sourceSets = [sourceSets.main, sourceSets.test, sourceSets.integrationTest]
}

pmdMain {
    ruleSetFiles = files "config/pmd/main.xml"
    excludes = ["**/generated/**/*.java"]
}

pmdTest {
    ruleSetFiles = files "config/pmd/test.xml"
}

pmdIntegrationTest {
    ruleSetFiles = files "config/pmd/test.xml"
}

task integrationTest(type: Test) {
    description = "Integration tests."
    group = "Verification"

    testClassesDirs = sourceSets.integrationTest.output.classesDirs
    classpath = sourceSets.integrationTest.runtimeClasspath
}

tasks.withType(Test) {
    useJUnitPlatform()
}

task copyDatadogJavaAgent(type: Copy) {
    from configurations.runtimeClasspath
    include 'dd-java-agent-*.jar'
    into project.libsDirectory
}

assemble.dependsOn copyDatadogJavaAgent

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder

task encodePassword {
    description "Encodes raw password for use in Spring configuration."
    doLast {
        def encodedPassword = new BCryptPasswordEncoder(strength as Integer).encode(password)
        println "{bcrypt}$encodedPassword"
    }
}

clean {
    delete "${project.projectDir}/generated"
}

bootRun {
    args = ["--spring.profiles.active=dev"]
}

jar {
    enabled = false
}

springBoot {
    buildInfo()
}
