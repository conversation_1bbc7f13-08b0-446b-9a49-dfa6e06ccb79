terraform {
  backend "remote" {
    hostname     = "tfe.mng.bseint.io"
    organization = "bestseller-ecom"

    workspaces {
      name = "sofs-acc"
    }
  }

  required_providers {
    aws = {
      source = "hashicorp/aws"
    }
  }
  required_version = ">= 1.0"
}

locals {
  # Setup - DO NOT CHANGE!
  aws_account_id = "************" # BSE-AWS-ACC
  aws_region     = "eu-west-1"

  # Project
  bseint_domain        = "bseint.io"
  env                  = "acc"
  project_information  = module.metadata.projects["sofs"]["metadata"]
  project              = local.project_information["project"]
  project_name         = local.project_information["project_name"]
  owner                = local.project_information["owner"]
  vcs                  = local.project_information["github_vcs"]
  jvm_memory_megabytes = 250

  s3_bucket_name = "bse-${local.project}-${local.env}.${local.bseint_domain}"
}

module "metadata" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/metadata/bestseller"
  version = "~> 2.0.0"
}

module "budget" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/budget/aws"
  version = "~> 2.0.2"

  env     = local.env
  project = local.project
  owner   = local.owner
  vcs     = local.vcs

  cost_estimate = "60"
}

module "secretsmanager-secret-fft-credentials" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/secretsmanager-secret/aws"
  version = "~> 1.2.1"

  env     = local.env
  owner   = local.owner
  project = local.project
  vcs     = local.vcs

  secret_name        = each.key
  secret_description = each.value.description
  secret_string      = each.value.string

  for_each = {
    fft_firebase_web_api_key = {
      description = "FulfillmentTools Firebase Web API key"
      string      = "AQECAHiJQdttwbIfqPJh8ZRJZlr7Pkf2H2IjkJ9tZViR1ZJDzAAAAIYwgYMGCSqGSIb3DQEHBqB2MHQCAQAwbwYJKoZIhvcNAQcBMB4GCWCGSAFlAwQBLjARBAyXH+AqzcymFCE5Am8CARCAQlIAwcaCs90n+Mx+IpZQh0WfOd6RRJau5Ce26wcbrHOoUe50N3cZ2FdkwgtFbwACSPcduAh5XGzb25gvwNGsHOainw=="
    }
    fft_password = {
      description = "FulfillmentTools password"
      string      = "AQECAHiJQdttwbIfqPJh8ZRJZlr7Pkf2H2IjkJ9tZViR1ZJDzAAAAGowaAYJKoZIhvcNAQcGoFswWQIBADBUBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDOjbU+d9XJCGU7tZ8wIBEIAnqIDg2SLEeWe9TlHiylw8NWmLNt129cphowj3iOvHX622+0kNtsXz"
    }
  }
}

module "ecr_management" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/ecr/aws"
  version = "~> 1.2.0"

  env     = local.env
  owner   = local.owner
  vcs     = local.vcs
  project = local.project
}

module "ecs_service" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/ecs/aws"
  version = "~> 1.2.0"

  env          = local.env
  project      = local.project
  project_name = local.project_name
  owner        = local.owner
  vcs          = local.vcs

  alb_listener_port      = 443
  alb_listener_rule_path = "/${local.project}/*"

  cluster_name                = "logistics-${local.env}"
  container_port              = 8080
  container_image             = "${local.aws_account_id}.dkr.ecr.eu-west-1.amazonaws.com/${local.project}:master"
  container_cpu_allocation    = 400
  container_memory_allocation = local.jvm_memory_megabytes + 300 # extra memory for non-heap data
  container_health_check_path = "/actuator/health"

  container_environment_vars = [
    for name, value in
    {
      JAVA_TOOL_OPTIONS = join(" ", [
        "-Xms${local.jvm_memory_megabytes / 2}m",
        "-Xmx${local.jvm_memory_megabytes}m",
        "-Dspring.profiles.active=${local.env}"
      ])
      KAFKA_BROKER_LIST = join(",", module.kafka_nodes.kafka_full_names)
      FFT_ENDPOINT      = "https://ocff-bestsellertest-pre.api.fulfillmenttools.com"
      FFT_AUTH_ENDPOINT = "https://identitytoolkit.googleapis.com/v1/"
      FFT_AUTH_EMAIL    = "<EMAIL>"

      DD_ENABLED                   = true
      DD_JMXFETCH_ENABLED          = true
      DD_PROFILING_ENABLED         = false
      DD_LOGS_INJECTION            = true
      DD_SERVICE                   = local.project
      DD_INTEGRATION_KAFKA_ENABLED = false
      S3_BUCKET                    = module.s3-bucket.bucket_domain
      PCS_ENDPOINT                 = "https://pcs.${local.env}.bseint.io/graphql"
      AWS_S3_BUCKET                = local.s3_bucket_name
    } :
    {
      name  = name
      value = value
    }
  ]

  container_environment_secrets = [
    {
      name      = "DD_API_KEY"
      valueFrom = data.aws_secretsmanager_secret_version.dd_api_key.arn
    },
    {
      name      = "FFT_AUTH_KEY"
      valueFrom = module.secretsmanager-secret-fft-credentials["fft_firebase_web_api_key"].secret_arn
    },
    {
      name      = "FFT_AUTH_PASSWORD"
      valueFrom = module.secretsmanager-secret-fft-credentials["fft_password"].secret_arn
    }
  ]

  iam_task_custom_policy_json = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "CR1777"
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:List*",
          "s3:PutObject",
          "s3:DeleteObject"
        ],
        Resource = [
          "arn:aws:s3:::${local.s3_bucket_name}/",
          "arn:aws:s3:::${local.s3_bucket_name}/*",
        ]
      }
    ]
  })
}

provider "aws" {
  region              = local.aws_region
  allowed_account_ids = [local.aws_account_id]
  assume_role {
    session_name = "terraform"
    role_arn     = "arn:aws:iam::${local.aws_account_id}:role/terraform-admin-role"
  }
}

# Get account ID
data "aws_caller_identity" "current" {
}

module "aws_tfe_schedule" {
  source     = "tfe.mng.bseint.io/bestseller-ecom/tfe-schedule/aws"
  version    = "~> 1.0.0"
  env        = local.env
  project    = local.project
  owner      = local.owner
  jira_board = "CRTS"
}

module "kafka_nodes" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/ec2-kafka-data/aws"
  version = "~> 1.3.0"

  env = local.env
}

module "s3-bucket" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/s3-bucket/aws"
  version = "~> 3.2.0"

  lifecycle_rules = [
    {
      id      = "expire-old-objects"
      enabled = true

      prefix = ""

      tags = {
        key   = "expire_with_lifecycle"
        value = "true"
      }

      expiration = {
        days = 90
      }
    }
  ]

  env     = local.env
  project = local.project
  owner   = local.owner
  vcs     = local.vcs

  s3_events = [
    {
      lambda_function_arn = data.aws_lambda_function.order_file_bundle_generator.arn
      events              = ["s3:ObjectCreated:*"]
      filter_prefix       = "fft-events/"
      filter_suffix       = ""
      id                  = "sofs-fft-events-${local.env}-id"
    },
    {
      lambda_function_arn = data.aws_lambda_function.order_file_bundle_generator.arn
      events              = ["s3:ObjectCreated:*"]
      filter_prefix       = "orders/"
      filter_suffix       = ""
      id                  = "sofs-orders-${local.env}-id"
    }
  ]
}

data "aws_lambda_function" "order_file_bundle_generator" {
  function_name = "logistic_lambdas-file-bundle-gen-indexing-${local.env}"
}

data "aws_secretsmanager_secret" "dd_api_key_metadata" {
  name = "DATADOG_API_KEY"
}
data "aws_secretsmanager_secret_version" "dd_api_key" {
  secret_id = data.aws_secretsmanager_secret.dd_api_key_metadata.name
}

