<?xml version="1.0"?>
<ruleset name="Custom ruleset for PMD"
         xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0
	http://pmd.sourceforge.net/ruleset_2_0_0.xsd">

    <description>
        This custom ruleset checks the code for problems.
    </description>

    <rule ref="category/java/design.xml/UseUtilityClass">
        <properties>
            <property name="violationSuppressXPath"
                      value="//ClassOrInterfaceDeclaration/preceding-sibling::Annotation/MarkerAnnotation/Name[@Image='SpringBootApplication']"/>
        </properties>
    </rule>

    <rule ref="category/java/bestpractices.xml">
        <!-- Rule disabled - We are using slf4j with parametrized logging, so log guarding has very little benefit.-->
        <exclude name="GuardLogStatement"/>
    </rule>

    <rule ref="category/java/codestyle.xml">
        <exclude name="AbstractNaming"/>
        <exclude name="LocalVariableCouldBeFinal"/>
        <exclude name="MethodArgumentCouldBeFinal"/>
        <exclude name="AtLeastOneConstructor"/>
        <exclude name="OnlyOneReturn"/>
    </rule>
    <rule ref="category/java/codestyle.xml/LongVariable">
        <properties>
            <property name="minimum" value="32"/>
        </properties>
    </rule>
    <rule ref="category/java/codestyle.xml/MethodNamingConventions">
        <properties>
            <property name="junit4TestPattern" value="((^|_)[a-z][a-zA-Z0-9]+){3}"/>
        </properties>
    </rule>
    <rule ref="category/java/codestyle.xml/ClassNamingConventions">
        <properties>
            <property name="utilityClassPattern" value="[A-Z][a-zA-Z0-9]+(Utils|Constants|Operations|Name|Generator)"/>
        </properties>
    </rule>

    <rule ref="category/java/design.xml">
        <exclude name="LoosePackageCoupling"/>
        <exclude name="LawOfDemeter"/>
        <exclude name="SignatureDeclareThrowsException"/>
        <exclude name="AvoidCatchingGenericException"/>
        <exclude name="ExcessiveImports"/>
    </rule>
    <rule ref="category/java/design.xml/NcssCount">
        <properties>
            <property name="methodReportLevel" value="26"/>
        </properties>
    </rule>
    <rule ref="category/java/documentation.xml/CommentSize">
        <properties>
            <property name="maxLines" value="15"/>
            <property name="maxLineLength" value="150"/>
        </properties>
    </rule>

    <rule ref="category/java/errorprone.xml">
        <exclude name="LoggerIsNotStaticFinal"/>
        <exclude name="DataflowAnomalyAnalysis"/>
        <exclude name="MissingSerialVersionUID"/>
        <exclude name="InvalidLogMessageFormat"/>
    </rule>

    <rule ref="category/java/multithreading.xml">
        <exclude name="UseConcurrentHashMap"/>
    </rule>

    <rule ref="category/java/performance.xml">
        <exclude name="AvoidInstantiatingObjectsInLoops"/>
        <exclude name="AddEmptyString"/>
    </rule>

    <rule ref="category/java/codestyle.xml/TooManyStaticImports">
        <properties>
            <property name="maximumStaticImports" value="30"/>
        </properties>
    </rule>

    <rule ref="category/java/codestyle.xml/ShortVariable">
        <properties>
            <property name="xpath">
                <value>
                    //VariableDeclaratorId[(string-length(@Name) &lt; $minimum) and (not (@Name='id'))]
                    (: ForStatement :)
                    [not(../../..[self::ForInit])]
                    (: Foreach statement :)
                    [not(../../..[self::ForStatement])]
                    (: Catch statement parameter :)
                    [not(../..[self::CatchStatement])]
                    (: Lambda expression parameter :)
                    [not(parent::LambdaExpression or ../../..[self::LambdaExpression])]
                </value>
            </property>
        </properties>
    </rule>
</ruleset>
