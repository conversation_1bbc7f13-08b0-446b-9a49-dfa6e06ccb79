name: Rollback
on: 
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: Select Environment to rollback
        options:
          - acc
          - prod
        required: true
      task-def-number:
        description: "The number of the task definition you want to rollback to. If empty the previous one from latest will be rollbacked"
        required: false
        default: ""
        type: string

jobs:
  RollbackAcc:
    secrets: inherit
    if: github.event.inputs.environment == 'acc'
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/rollback-ecs.yml@v2
    with:
      project-name: sofs
      owner: fulfilment
      cluster-name: logistics-acc
      environment: acc
      task-def-number: ${{ inputs.task-def-number }}

  RollbackProd:
    secrets: inherit
    if: github.event.inputs.environment == 'prod'
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/rollback-ecs.yml@v2
    with:
      project-name: sofs
      owner: fulfilment
      cluster-name: logistics-prod
      environment: prod
      task-def-number: ${{ inputs.task-def-number }}
