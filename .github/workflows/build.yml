name: Build
on:
  workflow_dispatch:
  push:
    branches:
      - master
    paths-ignore:
      - 'infra/**'
      - 'README.md'
      - '**/README.md'
  pull_request:
    branches:
      - master
    paths-ignore:
      - 'infra/**'
      - 'README.md'
      - '**/README.md'
jobs:
  Build:
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/build-ecs-java.yml@v2
    with:
      project-name: sofs
      environment: acc
      java-version: 17
      build-tool: gradle
