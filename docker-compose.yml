services:
  zookeeper:
    image: 381841683508.dkr.ecr.eu-west-1.amazonaws.com/bse-zookeeper:1
    ports:
      - "2181:2181"

  kafka:
    image: 381841683508.dkr.ecr.eu-west-1.amazonaws.com/bse-kafka:2.6.0
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_ADVERTISED_HOST_NAME: localhost
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_CREATE_TOPICS: FulfillmentToolsEventsReceived:1:1,OrderLineExported:1:1,OrderPartsCreated:1:1,OrderLineAcknowledged:1:1,OrderLineDispatched:1:1,OrderPartRejected:1:1

  wiremock:
    image: 381841683508.dkr.ecr.eu-west-1.amazonaws.com/bse-wiremock:2.33.2-alpine
    ports:
      - "9999:8080"
    volumes:
      - ./config/wiremock/:/wiremock/
    command:
      --verbose --global-response-templating --root-dir /wiremock
  localstack:
    image: localstack/localstack:1.3.0
    ports:
      - "4566:4566"
    environment:
      - SERVICES=s3
      - DEBUG=${DEBUG-}
      - DEFAULT_REGION=eu-west-1
      - DATA_DIR=${DATA_DIR-}
      - DOCKER_HOST=unix:///var/run/docker.sock
      - HOSTNAME=localstack
    volumes:
      - "${TMP_DIR:-/tmp/localstack}:/var/lib/localstack"
