---
version: '3.1'

services:
  zookeeper:
    image: 381841683508.dkr.ecr.eu-west-1.amazonaws.com/bse-zookeeper:1
    ports:
      - 2181

  kafka:
    image: 381841683508.dkr.ecr.eu-west-1.amazonaws.com/bse-kafka:2.6.0
    depends_on:
      - zookeeper
    ports:
      - 9092
    environment:
      KAFKA_ADVERTISED_HOST_NAME: kafka
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_CREATE_TOPICS: FulfillmentToolsEventsReceived:1:1,OrderLineExported:1:1,OrderPartsCreated:1:1,OrderLineAcknowledged:1:1,OrderLineDispatched:1:1,OrderPartRejected:1:1

  wiremock:
    image: 381841683508.dkr.ecr.eu-west-1.amazonaws.com/bse-wiremock:2.33.2-alpine
    ports:
      - 8080
    volumes:
      - ../config/wiremock/:/wiremock/
    command:
      --verbose --global-response-templating --root-dir /wiremock

  localstack:
    image: localstack/localstack:1.3.0
    ports:
      - 4566
    environment:
      - SERVICES=s3
      - DEBUG=${DEBUG-}
      - DEFAULT_REGION=eu-west-1
      - DATA_DIR=${DATA_DIR-}
      - DOCKER_HOST=unix:///var/run/docker.sock
      - HOSTNAME=localstack
    volumes:
      - "${TMP_DIR:-/tmp/localstack}:/var/lib/localstack"

  gradle:
    image: library/gradle:7.5.1-jdk17
    depends_on:
      - kafka
    volumes:
      - ..:/home/<USER>/project/
    working_dir: /home/<USER>/project/
    command: $GRADLE_COMMAND
    environment:
      KAFKA_BROKER_LIST: kafka
      ORG_GRADLE_PROJECT_githubAccessToken: $ORG_GRADLE_PROJECT_githubAccessToken
      WIREMOCK_PORT: 8080
      WIREMOCK_HOST: wiremock
      AWS_LOCAL_S3_ENDPOINT: http://localstack:4566
