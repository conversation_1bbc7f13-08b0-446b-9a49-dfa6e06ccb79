#! /bin/bash -e

echo Bash version: "$BASH_VERSION"
aws --version

GRADLE_OPTIONS=(--console=plain --no-daemon --full-stacktrace --info)
export GRADLE_COMMAND="gradle ${GRADLE_OPTIONS[*]} build integrationTest"

# Pick either personal or BSE access token whichever is available, print error otherwise
export ORG_GRADLE_PROJECT_githubAccessToken
: ${ORG_GRADLE_PROJECT_githubAccessToken=${GITHUB_TOKEN?}}

export USERID=$(id -u)
export GID=$(id -g)
export COMPOSE_FILE="$(dirname $0)/docker-compose.yml"
export COMPOSE_PROJECT_NAME=sofs
COMPOSE_OPTIONS=(--abort-on-container-exit --exit-code-from gradle --no-build)

docker compose down --volumes --remove-orphans
docker compose up "${COMPOSE_OPTIONS[@]}"
