# Store Order Fulfillment Service #
This project is responsible for fulfilling e-commerce orders from the physical retail stores. 
It achieves that by submitting order parts to FFT (Fulfillment Tools) which is an external company that provides 
a user interface to fulfill the order line items from the physical stores.
Once the order processing starts in the FFT, SOFS receives the order lines status from FFT and maps them to 
e-commerce events by producing relevant kafka messages.

## Owned by
Fulfilment

## Prerequisites

1. AWS credentials setup for production (for Docker login)
2. AWS CLI v2
3. JDK 17
4. Gradle

## Environment setup

1. Put your personal GitHub credentials in [Gradle property file][gradle_props] as follows

        githubAccessToken=****
   Alternatively, expose the credentials by defining environment variables

        ORG_GRADLE_PROJECT_githubAccessToken

   To create a new token, follow the [Github PAT guide][pat].

   The token should have permission `read:packages`

   
2. Start the app, then should see `{"status":"UP"}` on the [health page][health].

        $ ./gradlew bootRun

## Password encoding

Encode new user passwords before storing them in configuration files.

    ./gradlew encodePassword -Pstrength=5 -Ppassword=qwerty

## Docker registry configuration

1. Configure your aws credentials

         aws configure

2. Configure docker credentials

         aws --profile {name-of-your-prod-profile} ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin ************.dkr.ecr.eu-west-1.amazonaws.com


[pat]: https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token "Personal Access Token"

[gradle_props]: https://docs.gradle.org/current/userguide/build_environment.html#sec:gradle_configuration_properties "Gradle properties"

[health]: http://localhost:8080/actuator/health
